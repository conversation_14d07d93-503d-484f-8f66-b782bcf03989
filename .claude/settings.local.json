{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(node:*)", "Bash(ls:*)", "Bash(npm create:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:bfirsh.github.io)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:assets.playunb.com)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:ruffle.rs)", "Ba<PERSON>(unzip:*)", "Bash(rm:*)", "Bash(find:*)", "WebFetch(domain:localhost)"], "deny": []}}