name: CI/CD

on:
  push:
    branches: '**'
  workflow_dispatch:

jobs:

  version:
    name: Bump Version
    runs-on: ubuntu-latest
    if: github.ref != 'refs/heads/master'
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}

      - run: |
          git config --global user.name 'CI/CD'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          npm version patch -m "Bump: %s"
          git push

  lint:
    name: Lint
    runs-on: ubuntu-latest
    needs: version
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}

      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm ci

      - name: "Lint"
        run: |
          git config --global user.name 'CI/CD'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          npm run lint || true
          git commit -am "Eslint: fix all" || true
          git push || true

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/master'
    permissions:
      contents: write
      pages: write
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          submodules: true

      - name: Setup Pages
        uses: actions/configure-pages@v3

      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm ci
 
      - name: Build
        run: |
          npm run build

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v1
        with:
          path: 'public_html'

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2

