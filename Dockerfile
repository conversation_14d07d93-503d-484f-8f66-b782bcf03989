# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files first
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci --ignore-scripts

# Copy source code
COPY . .

# Build static application
ARG VERSION=dev
ARG NEXT_PUBLIC_FORMSPREE_ENDPOINT
ENV NEXT_PUBLIC_FORMSPREE_ENDPOINT=${NEXT_PUBLIC_FORMSPREE_ENDPOINT}
RUN VERSION=${VERSION} npm run build

# Production stage with nginx
FROM nginx:alpine

# Copy built static files to nginx html directory
COPY --from=builder /app/out /usr/share/nginx/html

# Copy optimized nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]