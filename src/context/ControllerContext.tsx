'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

interface ControllerContextType {
  isVisible: boolean;
  toggleController: () => void;
  simulateKeyEvent: (keyCode: number, type: 'keydown' | 'keyup') => void;
}

const ControllerContext = createContext<ControllerContextType | undefined>(undefined);

export function ControllerProvider({ children }: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const isMobileDevice = useCallback(() => {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
           'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  useEffect(() => {
    try {
      const savedVisible = localStorage.getItem('gameControllerVisible');
      if (savedVisible !== null) {
        setIsVisible(JSON.parse(savedVisible));
      } else {
        setIsVisible(isMobileDevice());
      }
    } catch (error) {
      console.error('Error loading controller visibility:', error);
      setIsVisible(isMobileDevice());
    }
    setIsInitialized(true);
  }, [isMobileDevice]);

  const toggleController = useCallback(() => {
    try {
      setIsVisible(prev => {
        const newVisible = !prev;
        try {
          localStorage.setItem('gameControllerVisible', JSON.stringify(newVisible));
        } catch (e) {
          console.warn('Could not save controller visibility to localStorage:', e);
        }
        return newVisible;
      });
    } catch (error) {
      console.error('Error toggling controller:', error);
    }
  }, []);

  const simulateKeyEvent = useCallback((keyCode: number, type: 'keydown' | 'keyup') => {
    try {
      // 确保在浏览器环境中执行
      if (typeof window === 'undefined' || typeof document === 'undefined') {
        return;
      }

      // 创建键盘事件
      const event = new KeyboardEvent(type, {
        bubbles: true,
        cancelable: true,
        view: window
      });

      // 安全地设置keyCode和which属性
      try {
        Object.defineProperty(event, 'keyCode', {
          value: keyCode,
          writable: false,
          configurable: true
        });
        Object.defineProperty(event, 'which', {
          value: keyCode,
          writable: false,
          configurable: true
        });
      } catch (e) {
        // 如果无法设置属性，忽略错误
        console.warn('Could not set keyCode/which properties:', e);
      }

      document.dispatchEvent(event);
    } catch (error) {
      console.error('Error simulating key event:', error);
    }
  }, []);

  const value = {
    isVisible: isInitialized ? isVisible : false,
    toggleController,
    simulateKeyEvent
  };

  return (
    <ControllerContext.Provider value={value}>
      {children}
    </ControllerContext.Provider>
  );
}

export function useController() {
  const context = useContext(ControllerContext);
  if (context === undefined) {
    throw new Error('useController must be used within a ControllerProvider');
  }
  return context;
}