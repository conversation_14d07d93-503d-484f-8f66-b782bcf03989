'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

interface ControllerContextType {
  isVisible: boolean;
  toggleController: () => void;
  simulateKeyEvent: (keyCode: number, type: 'keydown' | 'keyup') => void;
}

const ControllerContext = createContext<ControllerContextType | undefined>(undefined);

export function ControllerProvider({ children }: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const isMobileDevice = useCallback(() => {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
           'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  useEffect(() => {
    const savedVisible = localStorage.getItem('gameControllerVisible');
    if (savedVisible !== null) {
      setIsVisible(JSON.parse(savedVisible));
    } else {
      setIsVisible(isMobileDevice());
    }
    setIsInitialized(true);
  }, [isMobileDevice]);

  const toggleController = useCallback(() => {
    console.log('ControllerContext: toggleController called');
    setIsVisible(prev => {
      const newVisible = !prev;
      console.log('ControllerContext: changing visibility from', prev, 'to', newVisible);
      localStorage.setItem('gameControllerVisible', JSON.stringify(newVisible));
      return newVisible;
    });
  }, []);

  const simulateKeyEvent = useCallback((keyCode: number, type: 'keydown' | 'keyup') => {
    const event = new KeyboardEvent(type, {
      keyCode,
      which: keyCode,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(event);
  }, []);

  const value = {
    isVisible: isInitialized ? isVisible : false,
    toggleController,
    simulateKeyEvent
  };

  return (
    <ControllerContext.Provider value={value}>
      {children}
    </ControllerContext.Provider>
  );
}

export function useController() {
  const context = useContext(ControllerContext);
  if (context === undefined) {
    throw new Error('useController must be used within a ControllerProvider');
  }
  return context;
}