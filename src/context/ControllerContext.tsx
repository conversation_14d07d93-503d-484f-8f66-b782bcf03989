'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

interface ControllerContextType {
  isVisible: boolean;
  toggleController: () => void;
  simulateKeyEvent: (keyCode: number, type: 'keydown' | 'keyup') => void;
}

const ControllerContext = createContext<ControllerContextType | undefined>(undefined);

export function ControllerProvider({ children }: { children: React.ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const isMobileDevice = useCallback(() => {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
           'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  useEffect(() => {
    const savedVisible = localStorage.getItem('gameControllerVisible');
    if (savedVisible !== null) {
      setIsVisible(JSON.parse(savedVisible));
    } else {
      setIsVisible(isMobileDevice());
    }
    setIsInitialized(true);
  }, [isMobileDevice]);

  const toggleController = useCallback(() => {
    setIsVisible(prev => {
      const newVisible = !prev;
      localStorage.setItem('gameControllerVisible', JSON.stringify(newVisible));
      return newVisible;
    });
  }, []);

  const simulateKeyEvent = useCallback((keyCode: number, type: 'keydown' | 'keyup') => {
    // 创建更兼容的键盘事件
    const event = new KeyboardEvent(type, {
      keyCode,
      which: keyCode,
      code: `Key${String.fromCharCode(keyCode)}`,
      key: String.fromCharCode(keyCode),
      bubbles: true,
      cancelable: true,
      view: window
    });

    // 为了兼容性，也设置已弃用的属性
    Object.defineProperty(event, 'keyCode', {
      value: keyCode,
      writable: false
    });
    Object.defineProperty(event, 'which', {
      value: keyCode,
      writable: false
    });

    document.dispatchEvent(event);
  }, []);

  const value = {
    isVisible: isInitialized ? isVisible : false,
    toggleController,
    simulateKeyEvent
  };

  return (
    <ControllerContext.Provider value={value}>
      {children}
    </ControllerContext.Provider>
  );
}

export function useController() {
  const context = useContext(ControllerContext);
  if (context === undefined) {
    throw new Error('useController must be used within a ControllerProvider');
  }
  return context;
}