import { notFound } from 'next/navigation';
import Link from 'next/link';
import games from '@/data/games';
import { findGameBySlug, generateSlug } from '@/utils/slug';
import GamePageClient from '@/components/GamePageClient';
import AdSense from '@/components/AdSense';
import { GameStructuredData } from '@/components/StructuredData';
import type { Metadata } from 'next';

interface GamePageProps {
  params: Promise<{ slug: string }>;
}

export async function generateStaticParams() {
  return games.map((game) => ({
    slug: generateSlug(game.name),
  }));
}

export async function generateMetadata({ params }: GamePageProps): Promise<Metadata> {
  const { slug } = await params;
  const game = findGameBySlug(games, slug);

  if (!game) {
    return {
      title: 'Game Not Found - PlayUnb',
    };
  }

  const canonicalUrl = `https://playunb.com/game/${slug}/`;

  return {
    title: `${game.name} - PlayUnb`,
    description: `Play ${game.name} free online. Unblocked game available anywhere!`,
    keywords: `${game.name}, unblocked games, free games, browser games, online games, school games`,
    authors: [{ name: "PlayUnb" }],
    creator: "PlayUnb",
    publisher: "PlayUnb",
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: `${game.name} - PlayUnb`,
      description: `Play ${game.name} free online. Unblocked game available anywhere!`,
      url: canonicalUrl,
      siteName: 'PlayUnb',
      locale: 'en_US',
      type: 'website',
      images: [{
        url: game.thumbnail,
        width: 800,
        height: 600,
        alt: `${game.name} game screenshot`,
      }],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${game.name} - PlayUnb`,
      description: `Play ${game.name} free online. Unblocked game available anywhere!`,
      images: [game.thumbnail],
    },
  };
}

export default async function GamePage({ params }: GamePageProps) {
  const { slug } = await params;
  const game = findGameBySlug(games, slug);

  if (!game) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <GameStructuredData game={game} slug={slug} />
      <header className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex items-center gap-3 sm:gap-4">
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              <span className="text-sm sm:text-base">Back to Games</span>
            </Link>
            <div className="flex-1 min-w-0">
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900 dark:text-white truncate">
                {game.name}
              </h1>
              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                {game.runner === 'UNITY' ? 'Unity WebGL Game' :
                  game.runner === 'IFRAME' ? 'HTML5 Game' :
                    game.runner === 'EMULATOR_NES' ? 'NES Retro Game' :
                      game.runner === 'EMULATOR_GBA' ? 'GBA Retro Game' :
                        game.runner === 'RUFFLE' ? 'Flash Game' :
                          'Emulated Game'}
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* Top Ad */}
        <div className="mb-6 flex justify-center">
          <AdSense adSlot="5864535301" className="max-w-full" />
        </div>

        <GamePageClient game={game} />

        {/* Bottom Ad */}
        <div className="mt-6 sm:mt-8 flex justify-center">
          <AdSense adSlot="5864535301" className="max-w-full" />
        </div>

        {/* Related Games */}
        <div className="mt-6 sm:mt-8 text-center">
          <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm px-4">
            Enjoying this game? Check out our other{' '}
            <Link href="/" className="text-blue-600 dark:text-blue-400 hover:underline">
              free unblocked games
            </Link>!
          </p>
        </div>
      </main>
    </div>
  );
}