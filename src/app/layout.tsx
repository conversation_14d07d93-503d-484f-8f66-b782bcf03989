import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { WebsiteStructuredData } from "@/components/StructuredData";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "PlayUnb - Free Unblocked Games for Anywhere",
  description: "Play free unblocked games anywhere! Access hundreds of games that work at school, work, or anywhere with internet restrictions. No downloads required.",
  keywords: "Free Unblocked Games for Anywhere, unblocked games, free games, school games, browser games, online games",
  authors: [{ name: "PlayUnb" }],
  creator: "PlayUnb",
  publisher: "PlayUnb",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://playunb.com'),
  alternates: {
    canonical: 'https://playunb.com/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "PlayUnb - Free Unblocked Games for Anywhere",
    description: "Play free unblocked games anywhere! Access hundreds of games that work at school, work, or anywhere with internet restrictions.",
    url: 'https://playunb.com/',
    siteName: 'PlayUnb',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "PlayUnb - Free Unblocked Games for Anywhere",
    description: "Play free unblocked games anywhere! Access hundreds of games that work at school, work, or anywhere with internet restrictions.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Tracking Scripts */}
        <script src="https://rybbit.frytea.com/api/script.js" data-site-id="5" defer></script>
        <script defer src="https://umami.frytea.com/script.js" data-website-id="6d8ac440-db3d-409a-928e-814fe24182ce"></script>

        {/* Google tag (gtag.js) */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-KQHCKGK2D5"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-KQHCKGK2D5');
            `,
          }}
        />

        {/* Adsense ad manager (adsbygoogle.js) - Google Adsense */}
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7296634171837358"
          crossOrigin="anonymous"></script>
        <script src="https://unpkg.com/@ruffle-rs/ruffle"></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebsiteStructuredData
          title="PlayUnb - Free Unblocked Games for Anywhere"
          description="Play free unblocked games anywhere! Access hundreds of games that work at school, work, or anywhere with internet restrictions. No downloads required."
        />
        {children}
      </body>
    </html>
  );
}
