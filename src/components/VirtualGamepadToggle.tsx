'use client';

import React from 'react';
import { useController } from '@/context/ControllerContext';

interface VirtualGamepadToggleProps {
  runner?: string;
}

export default function VirtualGamepadToggle({
  runner
}: VirtualGamepadToggleProps) {
  // React hooks必须在组件顶层调用，不能在try-catch中
  const { isVisible, toggleController } = useController();

  // Only show for emulator games
  const showToggle = runner === 'EMULATOR_NES' || runner === 'EMULATOR_GBA';

  if (!showToggle) return null;

  const handleToggle = (e: React.MouseEvent) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      toggleController();
    } catch (error) {
      console.error('Error toggling controller:', error);
    }
  };

  return (
    <div className="absolute top-4 right-4 z-50 pointer-events-auto">
      <button
        onClick={handleToggle}
        onTouchStart={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
        }}
        className={`
          flex items-center justify-center
          w-10 h-10 sm:w-12 sm:h-12
          ${isVisible
            ? 'bg-purple-700 hover:bg-purple-800 shadow-purple-500/25'
            : 'bg-purple-600 hover:bg-purple-700 shadow-purple-400/25'
          }
          active:bg-purple-800 active:scale-90
          text-white rounded-full shadow-lg
          transition-all duration-200 ease-in-out
          hover:scale-105 hover:shadow-xl
          backdrop-blur-sm
          touch-manipulation
          select-none
          cursor-pointer
          pointer-events-auto
          border-2 border-white/20
        `}
        title={isVisible ? 'Hide virtual gamepad' : 'Show virtual gamepad'}
        aria-label={isVisible ? 'Hide virtual gamepad' : 'Show virtual gamepad'}
      >
        {/* Gamepad Icon */}
        <svg
          className="w-5 h-5 sm:w-6 sm:h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M15.5 12c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5zm-2.5-6c-3.31 0-6 2.69-6 6 0 1.66.67 3.16 1.76 4.24l.71-.71C8.54 14.61 8 13.36 8 12c0-2.21 1.79-4 4-4s4 1.79 4 4c0 1.36-.54 2.61-1.47 3.53l.71.71C16.33 15.16 17 13.66 17 12c0-3.31-2.69-6-6-6zm-4 6c0-.83.67-1.5 1.5-1.5S12 11.17 12 12s-.67 1.5-1.5 1.5S7.5 12.83 7.5 12z"/>
        </svg>

        {/* Status indicator */}
        <div className={`
          absolute -top-1 -right-1 w-3 h-3 rounded-full
          ${isVisible
            ? 'bg-green-400 shadow-green-400/50 animate-pulse'
            : 'bg-gray-400 shadow-gray-400/50'
          }
          transition-all duration-300 ease-in-out
          border border-white/30
        `} />
      </button>
    </div>
  );
}
