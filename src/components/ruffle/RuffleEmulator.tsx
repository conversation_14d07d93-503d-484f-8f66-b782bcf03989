'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Game } from '@/types/Game';
import { calculateGameLayout } from '@/utils/gameLayout';
import { PlayerElement, RufflePlayer, ReadyState, AutoPlay, LogLevel, WindowMode } from '@/types/ruffle';

interface RuffleEmulatorProps {
  game: Game;
  onLoadingChange?: (loading: boolean) => void;
  paused?: boolean;
}

interface RuffleEmulatorState {
  loading: boolean;
  error: string | null;
  progress: number;
  userInteracted: boolean;
  readyState: ReadyState;
}

export default function RuffleEmulator({
  game,
  onLoadingChange,
  paused = false
}: RuffleEmulatorProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<PlayerElement | null>(null);
  const [state, setState] = useState<RuffleEmulatorState>({
    loading: true,
    error: null,
    progress: 0,
    userInteracted: false,
    readyState: ReadyState.HAVE_NOTHING
  });

  const gameLayout = calculateGameLayout(game);

  // 通知父组件加载状态变化
  useEffect(() => {
    onLoadingChange?.(state.loading);
  }, [state.loading, onLoadingChange]);

  // 处理用户交互，启用音频
  const handleUserInteraction = useCallback(() => {
    if (!state.userInteracted) {
      setState(prev => ({ ...prev, userInteracted: true }));
      
      // 如果播放器已经准备好，确保开始播放
      if (playerRef.current && playerRef.current.readyState >= ReadyState.HAVE_METADATA) {
        playerRef.current.play();
      }
    }
  }, [state.userInteracted]);

  // 加载Ruffle脚本
  const loadRuffleScript = useCallback(async (): Promise<void> => {
    if ((window as any).RufflePlayer) {
      return;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/@ruffle-rs/ruffle@latest/ruffle.js';
      script.onload = () => {
        // 等待Ruffle初始化
        setTimeout(() => {
          if ((window as any).RufflePlayer) {
            resolve();
          } else {
            reject(new Error('Ruffle failed to initialize'));
          }
        }, 100);
      };
      script.onerror = () => reject(new Error('Failed to load Ruffle script'));
      document.head.appendChild(script);
    });
  }, []);

  // 初始化Ruffle播放器
  const initializePlayer = useCallback(async () => {
    if (!containerRef.current) return;

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // 加载Ruffle脚本
      await loadRuffleScript();

      const ruffle = ((window as any).RufflePlayer as RufflePlayer)?.newest();
      if (!ruffle) {
        throw new Error('Ruffle player not available');
      }

      // 创建播放器实例
      const player = ruffle.createPlayer();
      playerRef.current = player;

      // 配置播放器样式
      player.style.width = '100%';
      player.style.height = '100%';
      player.style.display = 'block';

      // 设置播放器配置
      const config = {
        allowScriptAccess: false,
        autoplay: AutoPlay.Auto,
        backgroundColor: null,
        logLevel: LogLevel.Warn,
        showSwfDownload: false,
        contextMenu: false,
        preloader: true,
        splashScreen: true,
        maxExecutionDuration: 30, // 30秒超时
        allowFullscreen: true,
        polyfills: false,
        warnOnUnsupportedContent: false,
        upgradeToHttps: true,
        compatibilityRules: true,
        favorFlash: false,
        publicPath: null,
        forceAlign: false,
        forceScale: false,
        menu: false,
        quality: 'high',
        scale: 'showAll',
        wmode: WindowMode.Window
      };

      // 添加事件监听器
      player.addEventListener('loadedmetadata', () => {
        setState(prev => ({ ...prev, readyState: ReadyState.HAVE_METADATA }));
      });

      player.addEventListener('canplay', () => {
        setState(prev => ({ ...prev, readyState: ReadyState.HAVE_ENOUGH_DATA }));
      });

      player.addEventListener('playing', () => {
        setState(prev => ({ ...prev, loading: false }));
      });

      player.addEventListener('error', (event: any) => {
        console.error('Ruffle player error:', event);
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: event.detail?.message || 'Failed to load Flash content' 
        }));
      });

      // 监听加载进度
      const checkProgress = () => {
        if (player.readyState >= ReadyState.HAVE_METADATA) {
          const progress = player.PercentLoaded();
          setState(prev => ({ ...prev, progress }));
          
          if (progress >= 100) {
            setState(prev => ({ ...prev, loading: false }));
          } else {
            setTimeout(checkProgress, 100);
          }
        } else {
          setTimeout(checkProgress, 100);
        }
      };

      // 将播放器添加到容器
      containerRef.current.appendChild(player);

      // 加载SWF文件
      await player.load({
        url: game.game,
        ...config
      });

      // 开始检查进度
      checkProgress();

    } catch (error) {
      console.error('Failed to initialize Ruffle player:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize Flash player'
      }));
    }
  }, [game.game, loadRuffleScript]);

  // 处理播放/暂停
  useEffect(() => {
    if (playerRef.current) {
      if (paused) {
        playerRef.current.pause();
      } else {
        playerRef.current.play();
      }
    }
  }, [paused]);

  // 组件挂载时初始化
  useEffect(() => {
    initializePlayer();

    // 清理函数
    return () => {
      if (playerRef.current && containerRef.current) {
        try {
          containerRef.current.removeChild(playerRef.current);
        } catch (e) {
          // 忽略清理错误
        }
      }
      playerRef.current = null;
    };
  }, [initializePlayer]);

  // 点击处理
  const handleClick = useCallback(() => {
    handleUserInteraction();
  }, [handleUserInteraction]);

  // 重试加载
  const handleRetry = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
    initializePlayer();
  }, [initializePlayer]);

  if (state.error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center p-6 max-w-md">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold mb-2">Flash Game Failed to Load</h3>
          <p className="text-sm text-gray-300 mb-4">{state.error}</p>
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className="w-full h-full relative bg-black flex items-center justify-center"
      onClick={handleClick}
    >
      {/* 播放器容器 */}
      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          aspectRatio: game.aspectRatio,
          objectFit: 'contain'
        }}
      />

      {/* 加载指示器 */}
      {state.loading && (
                 <div className="absolute inset-0 bg-black bg-opacity-80 flex items-center justify-center">
           <div className="text-center text-white">
             <div className="w-12 h-12 sm:w-16 sm:h-16 border-4 border-orange-500 border-t-transparent border-solid rounded-full animate-spin mx-auto mb-4"></div>
             <p className="text-base sm:text-lg font-semibold">Loading Flash Game...</p>
             <p className="text-xs sm:text-sm opacity-75 mt-2">
               {state.progress > 0 ? `${state.progress}%` : 'Initializing...'}
             </p>
             <p className="text-xs opacity-50 mt-1">
               {gameLayout.width}×{gameLayout.height}
             </p>
           </div>
         </div>
      )}

      {/* 点击激活提示 */}
             {!state.loading && !state.userInteracted && (
         <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
           <div className="text-center text-white">
             <div className="text-4xl mb-4">🖱️</div>
             <p className="text-lg font-semibold">Click to Start Game</p>
             <p className="text-sm opacity-75 mt-2">Click anywhere to enable audio and start playing</p>
           </div>
         </div>
       )}

      {/* 控制信息覆盖层 */}
             {!state.loading && (
         <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white p-2 rounded text-xs opacity-0 hover:opacity-100 transition-opacity">
           <p className="font-semibold mb-1">Flash Game Controls:</p>
           <p>Mouse: Click and drag</p>
           <p>Keyboard: Follow game instructions</p>
           <p>Right-click: May show context menu</p>
         </div>
       )}
    </div>
  );
} 