import { Game } from '@/types/Game';

interface GameStructuredDataProps {
  game: Game;
  slug: string;
}

export function GameStructuredData({ game, slug }: GameStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "VideoGame",
    "name": game.name,
    "description": `Play ${game.name} free online. Unblocked game available anywhere!`,
    "url": `https://playunb.com/game/${slug}/`,
    "image": game.thumbnail,
    "publisher": {
      "@type": "Organization",
      "name": "PlayUnb",
      "url": "https://playunb.com/"
    },
    "genre": "Browser Game",
    "gamePlatform": "Web Browser",
    "operatingSystem": "Any",
    "applicationCategory": "Game",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "ratingCount": "100",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

interface WebsiteStructuredDataProps {
  title: string;
  description: string;
}

export function WebsiteStructuredData({ title, description }: WebsiteStructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": title,
    "description": description,
    "url": "https://playunb.com/",
    "publisher": {
      "@type": "Organization",
      "name": "PlayUnb",
      "url": "https://playunb.com/",
      "logo": {
        "@type": "ImageObject",
        "url": "https://playunb.com/logo.png"
      }
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://playunb.com/?search={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
