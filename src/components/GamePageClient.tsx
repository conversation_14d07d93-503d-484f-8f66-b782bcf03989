'use client';

import { useState, useEffect, useRef } from 'react';
import { Game } from '@/types/Game';
import { getGameContainerStyle, getGameDisplayInfo } from '@/utils/gameLayout';
import GameControls from '@/components/GameControls';
import GameContainer from '@/components/GameContainer';
import GameInstructions from '@/components/GameInstructions';
import NESController from '@/components/controllers/NESController';
import GBAController from '@/components/controllers/GBAController';
import VirtualGamepadToggle from '@/components/VirtualGamepadToggle';
import { ControllerProvider } from '@/context/ControllerContext';

interface GamePageClientProps {
  game: Game;
}

export default function GamePageClient({ game }: GamePageClientProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isBrowserFullscreen, setIsBrowserFullscreen] = useState(false);

  const gameContainerStyle = getGameContainerStyle(game);
  const gameDisplayInfo = getGameDisplayInfo(game);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsBrowserFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleBrowserFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  return (
    <ControllerProvider>
      <div className={`${isBrowserFullscreen ? 'h-screen flex flex-col bg-black' : 'bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden'} relative`}>
        {/* Game Container */}
        <div className={`flex justify-center items-center ${isBrowserFullscreen ? 'flex-1 p-0' : 'p-4 sm:p-6 bg-gray-100 dark:bg-gray-700'}`}>
          <div
            ref={containerRef}
            className={`relative bg-black overflow-hidden ${isBrowserFullscreen ? 'w-full h-full' : 'rounded-lg shadow-lg'}`}
            style={isBrowserFullscreen ? {} : gameContainerStyle}
          >
            <GameContainer game={game} />
          </div>
        </div>

        {/* Virtual Gamepad Toggle - Only show in non-fullscreen mode */}
        {!isBrowserFullscreen && (
          <VirtualGamepadToggle
            runner={game.runner}
            isFullscreen={false}
          />
        )}
        
        {/* Game Info and Controls */}
        {!isBrowserFullscreen && (
          <div className="p-4 sm:p-6">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
                  {game.name}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  Free to play • No downloads required • 
                  {game.runner === 'UNITY' ? ' Unity WebGL' : 
                   game.runner === 'IFRAME' ? ' HTML5' : 
                   game.runner === 'EMULATOR_NES' ? ' NES Emulated' :
                   game.runner === 'EMULATOR_GBA' ? ' GBA Emulated' :
                   game.runner === 'RUFFLE' ? ' Flash (Ruffle)' :
                   ' Emulated'}
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {gameDisplayInfo.originalSize}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    {gameDisplayInfo.optimizedFor}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    {gameDisplayInfo.displayType}
                  </span>
                </div>
              </div>
              
              <div className="flex-shrink-0">
                <GameControls 
                  gameName={game.name} 
                  runner={game.runner}
                  onBrowserFullscreen={handleBrowserFullscreen}
                  isBrowserFullscreen={isBrowserFullscreen}
                />
              </div>
            </div>
            
            {/* Game Instructions */}
            <GameInstructions game={game} />
          </div>
        )}
        
        {/* Game Controllers */}
        {game.runner === 'EMULATOR_NES' && <NESController isFullscreen={isBrowserFullscreen} />}
        {game.runner === 'EMULATOR_GBA' && <GBAController isFullscreen={isBrowserFullscreen} />}
        
        {/* Fullscreen Controls */}
        {isBrowserFullscreen && (
          <>
            <div className="fixed top-4 left-4 z-[9998]">
              <GameControls
                gameName={game.name}
                runner={game.runner}
                onBrowserFullscreen={handleBrowserFullscreen}
                isBrowserFullscreen={isBrowserFullscreen}
              />
            </div>

            {/* Virtual Gamepad Toggle in fullscreen mode */}
            <VirtualGamepadToggle
              runner={game.runner}
              isFullscreen={true}
            />
          </>
        )}
      </div>
    </ControllerProvider>
  );
} 