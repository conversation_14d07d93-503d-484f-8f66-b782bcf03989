'use client';

import Link from 'next/link';
import { useController } from '@/context/ControllerContext';

interface GameControlsProps {
  gameName: string;
  runner?: string;
  onBrowserFullscreen?: () => void;
  isBrowserFullscreen?: boolean;
}

export default function GameControls({
  gameName,
  runner,
  onBrowserFullscreen,
  isBrowserFullscreen = false
}: GameControlsProps) {
  const { isVisible, toggleController } = useController();

  const showControllerButton = runner === 'EMULATOR_NES' || runner === 'EMULATOR_GBA';

  return (
    <div className="flex gap-2">
      <Link
        href="/"
        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium"
      >
        More Games
      </Link>

      {/* Browser Fullscreen Button */}
      {onBrowserFullscreen && (
        <button
          onClick={onBrowserFullscreen}
          className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
          title={isBrowserFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
        >
          {isBrowserFullscreen ? (
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 5.414V8a1 1 0 01-2 0V4zM3 12a1 1 0 011 1v2.586l2.293-2.293a1 1 0 111.414 1.414L5.414 17H8a1 1 0 010 2H4a1 1 0 01-1-1v-4a1 1 0 011-1zm10-8a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L15.586 5H13a1 1 0 010-2zm4 8a1 1 0 00-1 1v2.586l-2.293-2.293a1 1 0 00-1.414 1.414L15.586 17H13a1 1 0 000 2h4a1 1 0 001-1v-4a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      )}

      <button
        onClick={() => window.location.reload()}
        className="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium"
        title="Reload game"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
}