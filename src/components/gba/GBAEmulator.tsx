'use client';

import { useEffect, useState } from 'react';

interface GBAEmulatorProps {
  romData: string;
  onLoadingChange: (loading: boolean) => void;
  paused: boolean;
}

export default function GBAEmulator({
  romData,
  onLoadingChange,
  paused
}: GBAEmulatorProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    onLoadingChange(isLoading);
  }, [isLoading, onLoadingChange]);

  // 添加调试信息，确保组件不会重新挂载
  useEffect(() => {
    console.log('GBAEmulator: Component mounted for ROM:', romData);
    return () => {
      console.log('GBAEmulator: Component will unmount for ROM:', romData);
    };
  }, [romData]);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  // const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}&ts=${Date.now()}`;
  const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}`;

  return (
    <div className="w-full h-full relative flex items-center justify-center bg-black">
      <iframe
        src={iframeSrc}
        title="GBA Emulator"
        className="border-0 w-full h-full"
        allowFullScreen
        loading="lazy"
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        allow="autoplay; fullscreen; microphone; camera"
        style={{
          imageRendering: 'pixelated',
          aspectRatio: '3/2', // GBA的原始宽高比
          objectFit: 'contain', // 保持宽高比，不拉伸
          maxWidth: '100%',
          maxHeight: '100%',
        }}
      />
    </div>
  );
}