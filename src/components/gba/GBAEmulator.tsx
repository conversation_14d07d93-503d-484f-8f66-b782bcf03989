'use client';

import { useEffect, useState } from 'react';

interface GBAEmulatorProps {
  romData: string;
  onLoadingChange: (loading: boolean) => void;
  paused: boolean;
  isFullscreen: boolean;
}

export default function GBAEmulator({
  romData,
  onLoadingChange,
  paused,
  isFullscreen
}: GBAEmulatorProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    onLoadingChange(isLoading);
  }, [isLoading, onLoadingChange]);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  // const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}&ts=${Date.now()}`;
  const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}`;

  return (
    <div className="w-full h-full relative">
      <iframe
        src={iframeSrc}
        title="GBA Emulator"
        className="w-full h-full border-0"
        allowFullScreen
        loading="lazy"
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        allow="autoplay; fullscreen; microphone; camera"
        style={{
          imageRendering: 'pixelated',
        }}
      />
    </div>
  );
}