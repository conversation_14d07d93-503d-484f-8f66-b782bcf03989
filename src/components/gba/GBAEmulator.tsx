'use client';

import { useEffect, useState, useRef } from 'react';

interface GBAEmulatorProps {
  romData: string;
  onLoadingChange: (loading: boolean) => void;
  paused: boolean;
}

export default function GBAEmulator({
  romData,
  onLoadingChange,
  paused
}: GBAEmulatorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    onLoadingChange(isLoading);
  }, [isLoading, onLoadingChange]);

  // 监听键盘事件并转发到iframe
  useEffect(() => {
    const handleKeyEvent = (event: KeyboardEvent) => {
      try {
        if (iframeRef.current && iframeRef.current.contentWindow) {
          // 转发键盘事件到iframe
          iframeRef.current.contentWindow.postMessage({
            type: 'keyEvent',
            eventType: event.type,
            keyCode: event.keyCode,
            which: event.which,
            key: event.key,
            code: event.code
          }, '*');
        }
      } catch (error) {
        console.warn('Could not forward key event to iframe:', error);
      }
    };

    if (typeof document !== 'undefined') {
      document.addEventListener('keydown', handleKeyEvent);
      document.addEventListener('keyup', handleKeyEvent);

      return () => {
        document.removeEventListener('keydown', handleKeyEvent);
        document.removeEventListener('keyup', handleKeyEvent);
      };
    }
  }, []);



  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  // const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}&ts=${Date.now()}`;
  const iframeSrc = `https://assets.playunb.com/src/IodineGBA/index.html?${romData}`;

  return (
    <div className="w-full h-full relative flex items-center justify-center bg-black">
      <iframe
        ref={iframeRef}
        src={iframeSrc}
        title="GBA Emulator"
        className="border-0 w-full h-full"
        allowFullScreen
        loading="lazy"
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        allow="autoplay; fullscreen; microphone; camera"
        style={{
          imageRendering: 'pixelated',
          aspectRatio: '3/2', // GBA的原始宽高比
          objectFit: 'contain', // 保持宽高比，不拉伸
          maxWidth: '100%',
          maxHeight: '100%',
        }}
      />
    </div>
  );
}