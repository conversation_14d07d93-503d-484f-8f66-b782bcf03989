'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { Game } from '@/types/Game';
import { calculateGameLayout } from '@/utils/gameLayout';

interface UnityGameDirectProps {
  game: Game;
  onLoadingChange?: (loading: boolean) => void;
  isFullscreen?: boolean;
}

declare global {
  interface Window {
    UnityLoader?: {
      instantiate: (canvas: HTMLCanvasElement, gameConfigUrl: string, config?: {
        onProgress?: (unityInstance: any, progress: number) => void;
        Module?: any;
      }) => any;
    };
  }
}

export default function UnityGameDirect({ 
  game, 
  onLoadingChange, 
  isFullscreen = false 
}: UnityGameDirectProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const unityInstanceRef = useRef<any>(null);
  
  // 使用 useMemo 缓存 gameLayout 计算结果，避免无限循环
  const gameLayout = useMemo(() => calculateGameLayout(game), [game]);

  // Notify parent component about loading state changes
  useEffect(() => {
    onLoadingChange?.(loading);
  }, [loading, onLoadingChange]);

  // Handle fullscreen canvas resize and scaling
  useEffect(() => {
    if (unityInstanceRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const updateCanvasSize = () => {
        // 现在GameContainer已经处理了等比缩放，Unity只需要填满容器
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.objectFit = 'contain';
      };
      
      updateCanvasSize();
      
      // Listen for resize events
      window.addEventListener('resize', updateCanvasSize);
      return () => window.removeEventListener('resize', updateCanvasSize);
    }
  }, [isFullscreen, game.aspectRatio]);

  useEffect(() => {
    let isMounted = true;

    const loadUnityGame = async () => {
      try {
        // Load Unity scripts
        if (!window.UnityLoader) {
          await loadScript('https://assets.playunb.com/src/js/unity-loader.min.js');
          await loadScript('https://assets.playunb.com/src/js/unity.min.js');
        }

        if (!isMounted || !canvasRef.current) return;

        const canvas = canvasRef.current;
        
        console.log('Starting Unity game:', game.name);
        console.log('Game layout:', gameLayout);
        console.log('Canvas:', canvas);

        if (window.UnityLoader) {
          console.log('Instantiating Unity...');
          const gameInstance = window.UnityLoader.instantiate(canvas, game.game, {
            onProgress: function(unityInstance: any, progress: number) {
              const percentage = Math.round(progress * 100);
              
              if (isMounted) {
                setProgress(percentage);
                console.log('Unity progress:', percentage + '%');
              }
              
              if (percentage >= 100) {
                console.log('Unity loading complete');
                unityInstanceRef.current = unityInstance;
                
                setTimeout(function() {
                  if (isMounted) {
                    console.log('Unity initialization complete');
                    setLoading(false);
                    
                    // Focus canvas
                    canvas.focus();
                    
                    // Handle Unity canvas setup
                    if (unityInstance?.Module?.canvas) {
                      const unityCanvas = unityInstance.Module.canvas;
                      
                      // If Unity created its own canvas, replace it with ours
                      if (unityCanvas !== canvas) {
                        console.log('Replacing Unity canvas with our canvas');
                        unityInstance.Module.canvas = canvas;
                        
                        // Remove Unity's canvas if it exists in the DOM
                        if (unityCanvas.parentNode) {
                          unityCanvas.parentNode.removeChild(unityCanvas);
                        }
                      }
                    }
                    
                    // Start render loop
                    if (unityInstance.Module?.ctx) {
                      const gl = unityInstance.Module.ctx;
                      gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
                      
                      // Request animation frame
                      if (unityInstance.Module.requestAnimationFrame) {
                        unityInstance.Module.requestAnimationFrame(() => {
                          console.log('Unity render loop started');
                        });
                      }
                      
                      // Resume main loop if needed
                      if (unityInstance.Module.resumeMainLoop) {
                        unityInstance.Module.resumeMainLoop();
                      }
                    }
                  }
                }, 1000);
              }
            },
            Module: {
              TOTAL_MEMORY: 268435456,
              errorhandler: null,
              compatibilitycheck: null,
              canvas: canvas,
              onRuntimeInitialized: function() {
                console.log('Unity runtime initialized');
                console.log('Target resolution:', gameLayout.width, 'x', gameLayout.height);
                // Ensure Unity is using our canvas
                if (this.canvas && this.canvas !== canvas) {
                  this.canvas = canvas;
                }
              }
            }
          });
          
          unityInstanceRef.current = gameInstance;
          
        } else {
          console.error('UnityLoader not available');
          throw new Error('UnityLoader not available');
        }
      } catch (err) {
        console.error('Unity loading error:', err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Failed to load Unity game');
          setLoading(false);
        }
      }
    };

    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
      });
    };

    loadUnityGame();

    return () => {
      isMounted = false;
    };
  }, [game]); // 只依赖 game，gameLayout 会在 game 改变时自动重新计算

  // Handle user interaction to activate rendering
  const handleCanvasClick = () => {
    if (canvasRef.current && unityInstanceRef.current) {
      console.log('Canvas clicked, activating Unity...');
      
      // Focus canvas
      canvasRef.current.focus();
      
      // Try to trigger rendering
      if (unityInstanceRef.current.Module?.ctx) {
        const gl = unityInstanceRef.current.Module.ctx;
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        
        // Resume main loop
        if (unityInstanceRef.current.Module.resumeMainLoop) {
          unityInstanceRef.current.Module.resumeMainLoop();
        }
      }
    }
  };

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center p-8 max-w-md mx-auto">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">Failed to Load Game</h3>
          <p className="text-gray-300 text-sm mb-4">{error}</p>
          <p className="text-gray-400 text-xs mb-4">
            Target resolution: {gameLayout.width}×{gameLayout.height}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-center justify-center bg-black relative">
      {/* Canvas */}
      <canvas
        ref={canvasRef}
        id="unity-canvas"
        width={gameLayout.width}
        height={gameLayout.height}
        onClick={handleCanvasClick}
        className={`
          block bg-black
          ${isFullscreen ? 'object-contain' : 'w-full h-full object-contain'}
          ${loading ? 'opacity-50' : 'opacity-100'}
          transition-opacity duration-300
        `}
        style={{ 
          cursor: 'default',
          maxWidth: '100%',
          maxHeight: '100%',
        }}
      />
      
      {/* Loading Indicator */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin mx-auto mb-3"></div>
            <p className="text-sm font-medium">Loading Unity...</p>
            <p className="text-xs opacity-75 mt-1">{progress}% Complete</p>
            <p className="text-xs opacity-50 mt-1">{gameLayout.width}×{gameLayout.height}</p>
          </div>
        </div>
      )}
      
      {/* Click to activate hint */}
      {!loading && !isFullscreen && (
        <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-xs opacity-0 hover:opacity-100 transition-opacity">
          Click to activate • {gameLayout.width}×{gameLayout.height}
        </div>
      )}
    </div>
  );
}