'use client';

import { useRef, useState, useEffect } from 'react';
import { Game } from '@/types/Game';
import { calculateGameLayout } from '@/utils/gameLayout';
import UnityGameDirect from './UnityGameDirect';
import NESEmulator from './nes/Emulator';
import RuffleEmulator from './ruffle/RuffleEmulator';
import GBAEmulator from './gba/GBAEmulator';

interface GameContainerProps {
  game: Game;
}

export default function GameContainer({ game }: GameContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const gameLayout = calculateGameLayout(game);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else {
        await containerRef.current.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleUnityLoadingChange = (loading: boolean) => {
    setIsLoading(loading);
  };

  return (
    <div className="relative w-full h-full">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-90 flex items-center justify-center z-10 rounded-lg">
          <div className="text-center text-white px-4">
            <div className="w-12 h-12 sm:w-16 sm:h-16 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-base sm:text-lg font-semibold">Loading {game.name}...</p>
            <p className="text-xs sm:text-sm opacity-75 mt-2">
              Optimized for {gameLayout.width}×{gameLayout.height}
            </p>
          </div>
        </div>
      )}


      {/* Game Container */}
      <div
        ref={containerRef}
        className="w-full h-full bg-black relative overflow-hidden rounded-lg"
        style={{
          ...(isFullscreen && {
            borderRadius: 0,
            width: '100vw',
            height: '100vh',
          })
        }}
      >
        {game.runner === 'UNITY' ? (
          <UnityGameDirect 
            game={game} 
            onLoadingChange={handleUnityLoadingChange}
            isFullscreen={isFullscreen}
          />
        ) : game.runner === 'EMULATOR_NES' ? (
          <NESEmulator
            romData={game.game}
            onLoadingChange={handleUnityLoadingChange}
            paused={false}
            isFullscreen={isFullscreen}
          />
        ) : game.runner === 'EMULATOR_GBA' ? (
          <GBAEmulator
            romData={game.game}
            onLoadingChange={handleUnityLoadingChange}
            paused={false}
            isFullscreen={isFullscreen}
          />
        ) : game.runner === 'RUFFLE' ? (
          <RuffleEmulator
            game={game}
            onLoadingChange={handleUnityLoadingChange}
            isFullscreen={isFullscreen}
            paused={false}
          />
        ) : (
          <iframe
            src={game.game}
            title={game.name}
            className="w-full h-full border-0"
            allowFullScreen
            loading="lazy"
            onLoad={handleLoad}
            allow="autoplay; fullscreen; microphone; camera"
          />
        )}
      </div>

      {/* Game Info Overlay (only when not fullscreen and not loading) */}
      {!isFullscreen && !isLoading && (
        <div className="absolute bottom-2 left-2 sm:bottom-4 sm:left-4 bg-black bg-opacity-50 text-white p-2 rounded-lg text-xs sm:text-sm opacity-0 hover:opacity-100 transition-opacity">
          <p className="font-semibold">{game.name}</p>
          <p className="text-xs opacity-75">
            {gameLayout.width}×{gameLayout.height} • 
            {game.runner === 'UNITY' ? ' Unity WebGL' : 
             game.runner === 'IFRAME' ? ' HTML5' : 
             game.runner === 'EMULATOR_NES' ? ' NES Emulated' :
             game.runner === 'EMULATOR_GBA' ? ' GBA Emulated' :
             ' Emulated'}
          </p>
        </div>
      )}
    </div>
  );
} 