'use client';

import React from 'react';
import { useController } from '@/context/ControllerContext';

interface NESControllerProps {
  isFullscreen?: boolean;
}

export default function NESController({ isFullscreen = false }: NESControllerProps) {
  const { isVisible, simulateKeyEvent } = useController();

  if (!isVisible) return null;

  const handlePress = (keyCode: number) => {
    simulateKeyEvent(keyCode, 'keydown');
  };

  const handleRelease = (keyCode: number) => {
    simulateKeyEvent(keyCode, 'keyup');
  };

  const buttonProps = (keyCode: number) => ({
    onTouchStart: (e: React.TouchEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onTouchEnd: (e: React.TouchEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseDown: (e: React.MouseEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onMouseUp: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    }
  });

  const containerClass = isFullscreen 
    ? 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-lg px-4'
    : 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4';

  return (
    <div className={containerClass}>
      <div className="bg-black bg-opacity-80 backdrop-blur-sm rounded-lg p-3 sm:p-4 flex items-center justify-between gap-4 sm:gap-8 w-full">
        {/* D-Pad */}
        <div className="grid grid-cols-3 gap-1">
          <div />
          <button
            {...buttonProps(38)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↑
          </button>
          <div />
          <button
            {...buttonProps(37)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ←
          </button>
          <div className="w-8 h-8 sm:w-10 sm:h-10" />
          <button
            {...buttonProps(39)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            →
          </button>
          <div />
          <button
            {...buttonProps(40)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↓
          </button>
          <div />
        </div>

        {/* Select/Start */}
        <div className="flex flex-col gap-2">
          <button
            {...buttonProps(17)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            SELECT
          </button>
          <button
            {...buttonProps(13)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            START
          </button>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 sm:gap-3">
          <button
            {...buttonProps(90)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            B
          </button>
          <button
            {...buttonProps(88)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            A
          </button>
        </div>
      </div>
    </div>
  );
}