'use client';

import React from 'react';
import { createPortal } from 'react-dom';
import { useController } from '@/context/ControllerContext';

interface GBAControllerProps {
  isFullscreen?: boolean;
}

export default function GBAController({ isFullscreen = false }: GBAControllerProps) {
  const { isVisible, simulateKeyEvent } = useController();

  if (!isVisible) return null;

  // Check if we're in the browser environment
  if (typeof window === 'undefined') return null;

  const handlePress = (keyCode: number) => {
    simulateKeyEvent(keyCode, 'keydown');
  };

  const handleRelease = (keyCode: number) => {
    simulateKeyEvent(keyCode, 'keyup');
  };

  const buttonProps = (keyCode: number) => ({
    onTouchStart: (e: React.TouchEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onTouchEnd: (e: React.TouchEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseDown: (e: React.MouseEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onMouseUp: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    }
  });

  const containerClass = isFullscreen
    ? 'fixed bottom-0 left-0 right-0 z-[9999] bg-black bg-opacity-90 p-4'
    : 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[9999] w-full max-w-lg px-4';

  const controllerContent = (
    <div className={containerClass}>
      <div className={`
        ${isFullscreen
          ? 'flex items-center justify-center max-w-4xl mx-auto bg-black bg-opacity-95'
          : 'bg-black bg-opacity-80 backdrop-blur-sm rounded-lg'
        }
        p-3 sm:p-4 flex items-center justify-between gap-3 sm:gap-6 w-full
      `}>
        {/* L Button */}
        <button
          {...buttonProps(81)}
          className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-purple-500 text-white rounded text-xs font-bold"
        >
          L
        </button>

        {/* D-Pad */}
        <div className="grid grid-cols-3 gap-1">
          <div />
          <button
            {...buttonProps(38)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↑
          </button>
          <div />
          <button
            {...buttonProps(37)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ←
          </button>
          <div className="w-8 h-8 sm:w-10 sm:h-10" />
          <button
            {...buttonProps(39)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            →
          </button>
          <div />
          <button
            {...buttonProps(40)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↓
          </button>
          <div />
        </div>

        {/* Select/Start */}
        <div className="flex flex-col gap-2">
          <button
            {...buttonProps(17)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            SELECT
          </button>
          <button
            {...buttonProps(13)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            START
          </button>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 sm:gap-3">
          <button
            {...buttonProps(90)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            B
          </button>
          <button
            {...buttonProps(88)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            A
          </button>
        </div>

        {/* R Button */}
        <button
          {...buttonProps(87)}
          className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-purple-500 text-white rounded text-xs font-bold"
        >
          R
        </button>
      </div>
    </div>
  );

  // Use portal to render outside the fullscreen container
  return createPortal(controllerContent, document.body);
}