interface GamepadControllerOptions {
  onButtonDown: (controller: number, button: number) => void;
  onButtonUp: (controller: number, button: number) => void;
}

interface GamepadState {
  buttons: { pressed: boolean }[];
  axes: number[];
}

interface ButtonInfo {
  gamepadId: string;
  type: 'button' | 'axis';
  code: number;
  value?: number;
}

interface GamepadConfig {
  playerGamepadId: (string | null)[];
  configs: {
    [gamepadId: string]: {
      buttons: {
        type: 'button' | 'axis';
        code: number;
        buttonId: number;
        value?: number;
      }[];
    };
  };
}

export default class GamepadController {
  private onButtonDown: (controller: number, button: number) => void;
  private onButtonUp: (controller: number, button: number) => void;
  private gamepadState: (GamepadState | undefined)[] = [];
  private buttonCallback: ((buttonInfo: ButtonInfo) => void) | null = null;
  private gamepadConfig?: GamepadConfig;

  constructor(options: GamepadControllerOptions) {
    this.onButtonDown = options.onButtonDown;
    this.onButtonUp = options.onButtonUp;
    this.gamepadState = [];
    this.buttonCallback = null;
  }

  disableIfGamepadEnabled = (callback: (controller: number, button: number) => void) => {
    return (playerId: number, buttonId: number) => {
      if (!this.gamepadConfig) {
        return callback(playerId, buttonId);
      }

      var playerGamepadId = this.gamepadConfig.playerGamepadId;
      if (!playerGamepadId || !playerGamepadId[playerId - 1]) {
        // allow callback only if player is not associated to any gamepad
        return callback(playerId, buttonId);
      }
    };
  };

  private _getPlayerNumberFromGamepad = (gamepad: Gamepad): number => {
    if (!this.gamepadConfig) return 1;
    
    if (this.gamepadConfig.playerGamepadId[0] === gamepad.id) {
      return 1;
    }

    if (this.gamepadConfig.playerGamepadId[1] === gamepad.id) {
      return 2;
    }

    return 1;
  };

  poll = () => {
    const gamepads = navigator.getGamepads ? navigator.getGamepads() : [];
    const usedPlayers: number[] = [];

    for (let gamepadIndex = 0; gamepadIndex < gamepads.length; gamepadIndex++) {
      const gamepad = gamepads[gamepadIndex];
      const previousGamepad = this.gamepadState[gamepadIndex];

      if (!gamepad) {
        continue;
      }

      if (!previousGamepad) {
        this.gamepadState[gamepadIndex] = {
          buttons: gamepad.buttons.map(b => ({ pressed: b.pressed })),
          axes: gamepad.axes.slice(0)
        };
        continue;
      }

      const buttons = gamepad.buttons;
      const previousButtons = previousGamepad.buttons;

      if (this.buttonCallback) {
        for (let code = 0; code < gamepad.axes.length; code++) {
          const axis = gamepad.axes[code];
          const previousAxis = previousGamepad.axes[code];

          if (axis === -1 && previousAxis !== -1) {
            this.buttonCallback({
              gamepadId: gamepad.id,
              type: "axis",
              code: code,
              value: axis
            });
          }

          if (axis === 1 && previousAxis !== 1) {
            this.buttonCallback({
              gamepadId: gamepad.id,
              type: "axis",
              code: code,
              value: axis
            });
          }
        }

        for (let code = 0; code < buttons.length; code++) {
          const button = buttons[code];
          const previousButton = previousButtons[code];
          if (button.pressed && !previousButton.pressed) {
            this.buttonCallback({
              gamepadId: gamepad.id,
              type: "button",
              code: code
            });
          }
        }
      } else if (this.gamepadConfig) {
        let playerNumber = this._getPlayerNumberFromGamepad(gamepad);
        if (usedPlayers.length < 2) {
          if (usedPlayers.indexOf(playerNumber) !== -1) {
            playerNumber++;
            if (playerNumber > 2) playerNumber = 1;
          }
          usedPlayers.push(playerNumber);

          if (this.gamepadConfig.configs[gamepad.id]) {
            const configButtons = this.gamepadConfig.configs[gamepad.id].buttons;

            for (let i = 0; i < configButtons.length; i++) {
              const configButton = configButtons[i];
              if (configButton.type === "button") {
                const code = configButton.code;
                const button = buttons[code];
                const previousButton = previousButtons[code];

                if (button.pressed && !previousButton.pressed) {
                  this.onButtonDown(playerNumber, configButton.buttonId);
                } else if (!button.pressed && previousButton.pressed) {
                  this.onButtonUp(playerNumber, configButton.buttonId);
                }
              } else if (configButton.type === "axis") {
                const code = configButton.code;
                const axis = gamepad.axes[code];
                const previousAxis = previousGamepad.axes[code];

                if (
                  axis === configButton.value &&
                  previousAxis !== configButton.value
                ) {
                  this.onButtonDown(playerNumber, configButton.buttonId);
                }

                if (
                  axis !== configButton.value &&
                  previousAxis === configButton.value
                ) {
                  this.onButtonUp(playerNumber, configButton.buttonId);
                }
              }
            }
          }
        }
      }

      this.gamepadState[gamepadIndex] = {
        buttons: buttons.map(b => {
          return { pressed: b.pressed };
        }),
        axes: gamepad.axes.slice(0)
      };
    }
  };

  promptButton = (f?: ((buttonInfo: ButtonInfo) => void) | null) => {
    if (!f) {
      this.buttonCallback = null;
    } else {
      this.buttonCallback = buttonInfo => {
        this.buttonCallback = null;
        f(buttonInfo);
      };
    }
  };

  loadGamepadConfig = () => {
    var gamepadConfig;
    try {
      const stored = localStorage.getItem("gamepadConfig");
      if (stored) {
        gamepadConfig = JSON.parse(stored);
      }
    } catch (e) {
      console.log("Failed to get gamepadConfig from localStorage.", e);
    }

    this.gamepadConfig = gamepadConfig;
  };

  setGamepadConfig = (gamepadConfig: GamepadConfig) => {
    try {
      localStorage.setItem("gamepadConfig", JSON.stringify(gamepadConfig));
      this.gamepadConfig = gamepadConfig;
    } catch (e) {
      console.log("Failed to set gamepadConfig in localStorage");
    }
  };

  startPolling = () => {
    if (!navigator.getGamepads) {
      return { stop: () => {} };
    }

    let stopped = false;
    const loop = () => {
      if (stopped) return;

      this.poll();
      requestAnimationFrame(loop);
    };
    requestAnimationFrame(loop);

    return {
      stop: () => {
        stopped = true;
      }
    };
  };
}