import React, { Component, RefObject } from "react";

const SCREEN_WIDTH = 256;
const SCREEN_HEIGHT = 240;

interface ScreenProps {
  onMouseDown?: (x: number, y: number) => void;
  onMouseUp?: () => void;
}

interface ScreenState {}

class Screen extends Component<ScreenProps, ScreenState> {
  canvas: HTMLCanvasElement | null = null;
  context: CanvasRenderingContext2D | null = null;
  imageData: ImageData | null = null;
  buf: ArrayBuffer | null = null;
  buf8: Uint8ClampedArray | null = null;
  buf32: Uint32Array | null = null;

  render() {
    return (
      <canvas
        className="max-w-full max-h-full"
        width={SCREEN_WIDTH}
        height={SCREEN_HEIGHT}
        onMouseDown={this.handleMouseDown}
        onMouseUp={this.props.onMouseUp}
        ref={canvas => {
          this.canvas = canvas;
        }}
        style={{
          imageRendering: 'pixelated' as any,
          aspectRatio: '16/15', // NES的原始宽高比
          objectFit: 'contain', // 保持宽高比，不拉伸
        }}
      />
    );
  }

  componentDidMount() {
    this.initCanvas();
    // Initial fit
    setTimeout(() => this.fitInParent(), 100);
  }

  componentDidUpdate(prevProps: ScreenProps) {
    this.initCanvas();
  }

  initCanvas() {
    if (!this.canvas) return;
    
    this.context = this.canvas.getContext("2d");
    if (!this.context) return;
    
    this.imageData = this.context.getImageData(
      0,
      0,
      SCREEN_WIDTH,
      SCREEN_HEIGHT
    );

    this.context.fillStyle = "black";
    // set alpha to opaque
    this.context.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // buffer to write on next animation frame
    this.buf = new ArrayBuffer(this.imageData.data.length);
    // Get the canvas buffer in 8bit and 32bit
    this.buf8 = new Uint8ClampedArray(this.buf);
    this.buf32 = new Uint32Array(this.buf);

    // Set alpha
    for (var i = 0; i < this.buf32.length; ++i) {
      this.buf32[i] = 0xff000000;
    }
  }

  setBuffer = (buffer: number[]) => {
    if (!this.buf32) return;
    
    var i = 0;
    for (var y = 0; y < SCREEN_HEIGHT; ++y) {
      for (var x = 0; x < SCREEN_WIDTH; ++x) {
        i = y * 256 + x;
        // Convert pixel from NES BGR to canvas ABGR
        this.buf32[i] = 0xff000000 | buffer[i]; // Full alpha
      }
    }
  };

  writeBuffer = () => {
    if (!this.context || !this.imageData || !this.buf8) return;
    
    this.imageData.data.set(this.buf8);
    this.context.putImageData(this.imageData, 0, 0);
  };

  fitInParent = () => {
    if (!this.canvas) return;
    
    let parent = this.canvas.parentNode as HTMLElement;
    if (!parent) return;
    
    let parentWidth = parent.clientWidth;
    let parentHeight = parent.clientHeight;
    let parentRatio = parentWidth / parentHeight;
    let desiredRatio = SCREEN_WIDTH / SCREEN_HEIGHT;
    
    if (desiredRatio < parentRatio) {
      this.canvas.style.width = `${Math.round(parentHeight * desiredRatio)}px`;
      this.canvas.style.height = `${parentHeight}px`;
    } else {
      this.canvas.style.width = `${parentWidth}px`;
      this.canvas.style.height = `${Math.round(parentWidth / desiredRatio)}px`;
    }
  };

  screenshot(): HTMLImageElement {
    if (!this.canvas) {
      throw new Error('Canvas not initialized');
    }
    
    var img = new Image();
    img.src = this.canvas.toDataURL("image/png");
    return img;
  }

  handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!this.props.onMouseDown || !this.canvas) return;
    
    // Make coordinates unscaled
    let scale = SCREEN_WIDTH / parseFloat(this.canvas.style.width || '256');
    let rect = this.canvas.getBoundingClientRect();
    let x = Math.round((e.clientX - rect.left) * scale);
    let y = Math.round((e.clientY - rect.top) * scale);
    this.props.onMouseDown(x, y);
  };
}

export default Screen;