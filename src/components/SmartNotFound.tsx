'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { getRedirectUrl, extractGameNameFromUrl, findBestMatchingGame, debugUrlMatching } from '@/utils/urlMatcher';

interface SmartNotFoundProps {
  title?: string;
  description?: string;
  showGameSuggestions?: boolean;
}

export default function SmartNotFound({
  title = "Page Not Found",
  description = "Sorry, we couldn't find the page you're looking for.",
  showGameSuggestions = true
}: SmartNotFoundProps) {
  const [countdown, setCountdown] = useState(5);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [suggestedGame, setSuggestedGame] = useState<any>(null);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Debug URL matching in development
    debugUrlMatching(pathname);

    // Try to find a matching game for redirect
    const redirect = getRedirectUrl(pathname);

    if (redirect) {
      setRedirectUrl(redirect);
      setCountdown(1); // 找到匹配时使用1秒倒计时

      // Also get the suggested game info for display
      const extractedName = extractGameNameFromUrl(pathname);
      if (extractedName && showGameSuggestions) {
        const match = findBestMatchingGame(extractedName);
        if (match) {
          setSuggestedGame(match);
        }
      }
    } else {
      // 没有找到匹配时，设置3秒后跳转到首页
      setRedirectUrl('/');
      setCountdown(3);
    }
  }, [pathname, showGameSuggestions]);

  useEffect(() => {
    if (!redirectUrl) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          router.push(redirectUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router, redirectUrl]);

  const handleRedirectNow = () => {
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      router.push('/');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4">
      <div className="text-center max-w-md">
        <h1 className="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
          {title}
        </h2>

        {suggestedGame ? (
          <div className="mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-center mb-2">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-blue-800 dark:text-blue-200 font-medium">Game Found!</span>
              </div>
              <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
                We found a similar game: <strong>{suggestedGame.game.name}</strong>
              </p>
              <p className="text-blue-600 dark:text-blue-400 text-sm">
                Redirecting in {countdown} seconds...
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleRedirectNow}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Go to {suggestedGame.game.name} Now
              </button>
              <Link
                href="/"
                className="block w-full bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium"
              >
                Browse All Games
              </Link>
            </div>
          </div>
        ) : (
          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {description} The page might have been moved or the URL is incorrect.
            </p>
            <p className="text-orange-600 dark:text-orange-400 mb-6">
              Redirecting to homepage in {countdown} seconds...
            </p>
            <div className="space-y-3">
              <button
                onClick={handleRedirectNow}
                className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Go to Homepage Now
              </button>
              <Link
                href="/"
                className="block text-blue-600 dark:text-blue-400 hover:underline"
              >
                ← Browse All Games
              </Link>
            </div>
          </div>
        )}

        {/* Debug info in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-6 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs text-left">
            <p><strong>Debug Info:</strong></p>
            <p>Current path: {pathname}</p>
            <p>Extracted name: {extractGameNameFromUrl(pathname) || 'None'}</p>
            <p>Redirect URL: {redirectUrl || 'None'}</p>
            {suggestedGame && (
              <p>Match similarity: {(suggestedGame.similarity * 100).toFixed(1)}%</p>
            )}
            <div className="mt-2 pt-2 border-t border-gray-300 dark:border-gray-600">
              <p><strong>Test URLs:</strong></p>
              <p className="text-blue-600 dark:text-blue-400">/g/123/celeste</p>
              <p className="text-blue-600 dark:text-blue-400">/g/456/falling-ball</p>
              <p className="text-blue-600 dark:text-blue-400">/celeste</p>
              <p className="text-blue-600 dark:text-blue-400">/mario</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
