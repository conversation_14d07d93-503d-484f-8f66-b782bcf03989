// Ruffle Flash emulator TypeScript definitions

export interface URLLoadOptions {
  url: string;
  allowScriptAccess?: boolean;
  parameters?: Record<string, string> | URLSearchParams | string | null;
  autoplay?: AutoPlay;
  backgroundColor?: string | null;
  letterbox?: Letterbox;
  unmuteOverlay?: UnmuteOverlay;
  upgradeToHttps?: boolean;
  compatibilityRules?: boolean;
  favorFlash?: boolean;
  warnOnUnsupportedContent?: boolean;
  logLevel?: LogLevel;
  showSwfDownload?: boolean;
  contextMenu?: boolean | ContextMenu;
  preloader?: boolean;
  splashScreen?: boolean;
  maxExecutionDuration?: number;
  base?: string | null;
  menu?: boolean;
  salign?: string;
  fullScreenAspectRatio?: string;
  forceAlign?: boolean;
  quality?: string | null;
  scale?: string;
  forceScale?: boolean;
  allowFullscreen?: boolean;
  frameRate?: number | null;
  wmode?: WindowMode;
  playerVersion?: number | null;
  preferredRenderer?: RenderBackend | null;
  publicPath?: string | null;
  polyfills?: boolean;
  openUrlMode?: OpenURLMode;
  allowNetworking?: NetworkingAccessMode;
  openInNewTab?: ((swf: URL) => void) | null;
  socketProxy?: SocketProxy[];
  fontSources?: string[];
  defaultFonts?: DefaultFonts;
  credentialAllowList?: string[];
  playerRuntime?: PlayerRuntime;
  gamepadButtonMapping?: Partial<Record<GamepadButton, number>>;
  urlRewriteRules?: [string | RegExp, string][];
  scrollingBehavior?: ScrollingBehavior;
}

export interface DataLoadOptions {
  data: Uint8Array;
  allowScriptAccess?: boolean;
  parameters?: Record<string, string> | URLSearchParams | string | null;
  autoplay?: AutoPlay;
  backgroundColor?: string | null;
  letterbox?: Letterbox;
  unmuteOverlay?: UnmuteOverlay;
  upgradeToHttps?: boolean;
  compatibilityRules?: boolean;
  favorFlash?: boolean;
  warnOnUnsupportedContent?: boolean;
  logLevel?: LogLevel;
  showSwfDownload?: boolean;
  contextMenu?: boolean | ContextMenu;
  preloader?: boolean;
  splashScreen?: boolean;
  maxExecutionDuration?: number;
  base?: string | null;
  menu?: boolean;
  salign?: string;
  fullScreenAspectRatio?: string;
  forceAlign?: boolean;
  quality?: string | null;
  scale?: string;
  forceScale?: boolean;
  allowFullscreen?: boolean;
  frameRate?: number | null;
  wmode?: WindowMode;
  playerVersion?: number | null;
  preferredRenderer?: RenderBackend | null;
  publicPath?: string | null;
  polyfills?: boolean;
  openUrlMode?: OpenURLMode;
  allowNetworking?: NetworkingAccessMode;
  openInNewTab?: ((swf: URL) => void) | null;
  socketProxy?: SocketProxy[];
  fontSources?: string[];
  defaultFonts?: DefaultFonts;
  credentialAllowList?: string[];
  playerRuntime?: PlayerRuntime;
  gamepadButtonMapping?: Partial<Record<GamepadButton, number>>;
  urlRewriteRules?: [string | RegExp, string][];
  scrollingBehavior?: ScrollingBehavior;
}

export interface MovieMetadata {
  width: number;
  height: number;
  frameRate: number;
  numFrames: number;
  swfVersion: number;
  backgroundColor: string | null;
  compressed: boolean;
  xMin: number;
  xMax: number;
  yMin: number;
  yMax: number;
}

export interface PlayerElement extends HTMLElement {
  ruffle<V extends 1 = 1>(version?: V): any;
  onFSCommand: null | ((command: string, args: string) => void);
  config: object | URLLoadOptions | DataLoadOptions;
  loadedConfig: null | URLLoadOptions | DataLoadOptions;
  readonly readyState: ReadyState;
  readonly metadata: null | MovieMetadata;
  reload(): Promise<void>;
  load(options: string | URLLoadOptions | DataLoadOptions): Promise<void>;
  play(): void;
  readonly isPlaying: boolean;
  volume: number;
  readonly fullscreenEnabled: boolean;
  readonly isFullscreen: boolean;
  setFullscreen(isFull: boolean): void;
  enterFullscreen(): void;
  exitFullscreen(): void;
  pause(): void;
  traceObserver: null | ((message: string) => void);
  downloadSwf(): Promise<void>;
  displayMessage(message: string): void;
  PercentLoaded(): number;
}

export interface RufflePlayer {
  newest(): RufflePlayer;
  createPlayer(): PlayerElement;
  config: any;
}

export enum ReadyState {
  HAVE_NOTHING = 0,
  HAVE_METADATA = 1,
  HAVE_CURRENT_DATA = 2,
  HAVE_FUTURE_DATA = 3,
  HAVE_ENOUGH_DATA = 4
}

export enum AutoPlay {
  On = 'on',
  Off = 'off',
  Auto = 'auto'
}

export enum Letterbox {
  None = 'none',
  Fullscreen = 'fullscreen',
  NoScale = 'no-scale'
}

export enum UnmuteOverlay {
  Visible = 'visible',
  Hidden = 'hidden'
}

export enum LogLevel {
  Error = 'error',
  Warn = 'warn',
  Info = 'info',
  Debug = 'debug',
  Trace = 'trace'
}

export enum ContextMenu {
  On = 'on',
  Off = 'off',
  RightClickOnly = 'rightClickOnly'
}

export enum WindowMode {
  Window = 'window',
  Opaque = 'opaque',
  Transparent = 'transparent',
  Direct = 'direct',
  Gpu = 'gpu'
}

export enum RenderBackend {
  Canvas = 'canvas',
  WebGL = 'webgl',
  WebGPU = 'webgpu',
  WgpuWebGL = 'wgpu-webgl'
}

export enum OpenURLMode {
  Allow = 'allow',
  Confirm = 'confirm',
  Deny = 'deny'
}

export enum NetworkingAccessMode {
  All = 'all',
  Internal = 'internal',
  None = 'none'
}

export enum PlayerRuntime {
  FlashPlayer = 'flashPlayer',
  AIR = 'air'
}

export enum GamepadButton {
  North = 'north',
  South = 'south',
  East = 'east',
  West = 'west',
  Start = 'start',
  Select = 'select',
  DPadUp = 'dpad-up',
  DPadDown = 'dpad-down',
  DPadLeft = 'dpad-left',
  DPadRight = 'dpad-right',
  LeftShoulder = 'left-shoulder',
  RightShoulder = 'right-shoulder',
  LeftTrigger = 'left-trigger',
  RightTrigger = 'right-trigger',
  LeftStick = 'left-stick',
  RightStick = 'right-stick'
}

export enum ScrollingBehavior {
  Smart = 'smart',
  Block = 'block',
  Allow = 'allow'
}

export interface SocketProxy {
  host: string;
  port: number;
  proxyUrl: string;
}

export interface DefaultFonts {
  serif?: string;
  sansSerif?: string;
  typewriter?: string;
  japanese?: string;
  korean?: string;
  simplifiedChinese?: string;
  traditionalChinese?: string;
}

declare global {
  interface Window {
    RufflePlayer?: RufflePlayer;
  }
} 