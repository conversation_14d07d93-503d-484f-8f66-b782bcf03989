declare module 'jsnes' {
  export interface NESOptions {
    onFrame?: (frameBuffer: number[]) => void;
    onAudioSample?: (left: number, right: number) => void;
    onStatusUpdate?: (status: string) => void;
    sampleRate?: number;
  }

  export class NES {
    constructor(options: NESOptions);
    
    loadROM(romData: Uint8Array | string): void;
    frame(): void;
    buttonDown(controller: number, button: number): void;
    buttonUp(controller: number, button: number): void;
    zapperMove(x: number, y: number): void;
    zapperFireDown(): void;
    zapperFireUp(): void;
    reset(): void;
    toJSON(): any;
    fromJSON(data: any): void;
    getFPS(): number;
  }

  export namespace Controller {
    export const BUTTON_A: number;
    export const BUTTON_B: number;
    export const BUTTON_SELECT: number;
    export const BUTTON_START: number;
    export const BUTTON_UP: number;
    export const BUTTON_DOWN: number;
    export const BUTTON_LEFT: number;
    export const BUTTON_RIGHT: number;
  }

  export { Controller };
}

declare module 'ringbufferjs' {
  interface RingBufferConstructor {
    new (capacity?: number, evictedCb?: (element: any) => void): RingBufferInstance;
  }
  
  interface RingBufferInstance {
    enq(element: any): number;
    deq(): any;
    deqN(count: number): any[];
    peek(): any;
    peekN(count: number): any[];
    size(): number;
    capacity(): number;
    isEmpty(): boolean;
    isFull(): boolean;
  }
  
  const RingBuffer: RingBufferConstructor;
  export = RingBuffer;
}