import { Game } from '@/types/Game';

export interface GameLayoutDimensions {
  width: number;
  height: number;
  maxWidth: string;
  maxHeight: string;
  aspectRatio: number;
}

/**
 * 计算游戏的最优布局尺寸
 * 考虑游戏的原始width和aspectRatio，同时保持响应式设计
 */
export function calculateGameLayout(game: Game): GameLayoutDimensions {
  const gameWidth = game.width;
  const gameHeight = Math.round(gameWidth / game.aspectRatio);
  
  // 计算不同屏幕尺寸下的最大尺寸
  // 移动端：较小的最大尺寸
  // 桌面端：根据游戏原始尺寸调整
  
  // 基于游戏原始尺寸确定显示策略
  let maxWidth: string;
  let maxHeight: string;
  
  if (gameWidth <= 800) {
    // 小尺寸游戏：允许更大的显示比例
    maxWidth = 'min(90vw, 1000px)';
    maxHeight = 'min(70vh, 750px)';
  } else if (gameWidth <= 1000) {
    // 中等尺寸游戏：保持平衡
    maxWidth = 'min(85vw, 1200px)';
    maxHeight = 'min(75vh, 800px)';
  } else {
    // 大尺寸游戏：限制最大尺寸避免过大
    maxWidth = 'min(80vw, 1400px)';
    maxHeight = 'min(80vh, 900px)';
  }
  
  return {
    width: gameWidth,
    height: gameHeight,
    maxWidth,
    maxHeight,
    aspectRatio: game.aspectRatio,
  };
}

/**
 * 为特定游戏类型生成CSS样式对象
 */
export function getGameContainerStyle(game: Game): React.CSSProperties {
  const layout = calculateGameLayout(game);
  
  return {
    aspectRatio: layout.aspectRatio,
    maxWidth: layout.maxWidth,
    maxHeight: layout.maxHeight,
    minHeight: '300px',
    minWidth: '300px',
    width: '100%',
    height: 'auto',
  };
}

/**
 * 获取游戏的显示信息
 */
export function getGameDisplayInfo(game: Game): {
  originalSize: string;
  displayType: string;
  optimizedFor: string;
} {
  const layout = calculateGameLayout(game);
  
  const displayType = game.width <= 800 ? 'Compact' : 
                     game.width <= 1000 ? 'Standard' : 'Large';
  
  const optimizedFor = game.aspectRatio > 1.5 ? 'Widescreen' :
                      game.aspectRatio < 1.2 ? 'Square' : 'Standard';
  
  return {
    originalSize: `${layout.width}×${layout.height}`,
    displayType,
    optimizedFor,
  };
} 