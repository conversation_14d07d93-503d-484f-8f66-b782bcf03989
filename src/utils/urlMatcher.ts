import games from '@/data/games';
import { generateSlug } from './slug';

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Calculate similarity score between two strings (0-1, higher is more similar)
 */
function calculateSimilarity(str1: string, str2: string): number {
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1;

  const distance = levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
  return (maxLength - distance) / maxLength;
}

/**
 * Extract game name from old URL patterns
 * Supports patterns like:
 * - /g/{id}/{game-name}
 * - /game/{game-name} (current format)
 * - /{game-name}
 */
export function extractGameNameFromUrl(url: string): string | null {
  // Remove query parameters and hash
  const cleanUrl = url.split('?')[0].split('#')[0];

  // Split by '/' and filter out empty parts
  const parts = cleanUrl.split('/').filter(part => part.length > 0);

  if (parts.length === 0) return null;

  // Handle different URL patterns
  if (parts[0] === 'g' && parts.length >= 3) {
    // Old format: /g/{id}/{game-name}
    return parts[2];
  } else if (parts[0] === 'game' && parts.length >= 2) {
    // Current format: /game/{game-name}
    return parts[1];
  } else if (parts.length === 1) {
    // Direct game name: /{game-name}
    return parts[0];
  } else if (parts.length >= 1) {
    // Take the last part as game name
    return parts[parts.length - 1];
  }

  return null;
}

/**
 * Find the best matching game based on extracted name
 */
export function findBestMatchingGame(extractedName: string): { game: any; slug: string; similarity: number } | null {
  if (!extractedName) return null;

  // Clean the extracted name (remove hyphens, underscores, etc.)
  const cleanExtracted = extractedName
    .toLowerCase()
    .replace(/[-_]/g, ' ')
    .trim();

  let bestMatch = null;
  let bestSimilarity = 0;

  for (const game of games) {
    const gameSlug = generateSlug(game.name);
    const cleanGameName = game.name.toLowerCase().replace(/[^a-z0-9\s]/g, ' ').trim();

    // Calculate similarity with different variations
    const similarities = [
      calculateSimilarity(cleanExtracted, cleanGameName),
      calculateSimilarity(cleanExtracted, gameSlug),
      calculateSimilarity(extractedName.toLowerCase(), gameSlug),
      calculateSimilarity(extractedName.toLowerCase(), cleanGameName),
    ];

    const maxSimilarity = Math.max(...similarities);

    // Also check for exact substring matches (higher priority)
    const isSubstring = cleanGameName.includes(cleanExtracted) ||
      cleanExtracted.includes(cleanGameName) ||
      gameSlug.includes(extractedName.toLowerCase()) ||
      extractedName.toLowerCase().includes(gameSlug);

    const finalSimilarity = isSubstring ? Math.max(maxSimilarity, 0.8) : maxSimilarity;

    if (finalSimilarity > bestSimilarity) {
      bestSimilarity = finalSimilarity;
      bestMatch = { game, slug: gameSlug, similarity: finalSimilarity };
    }
  }

  // Only return matches with reasonable similarity (> 0.6)
  return bestMatch && bestMatch.similarity > 0.6 ? bestMatch : null;
}

/**
 * Get redirect URL for a given path
 */
export function getRedirectUrl(currentPath: string): string | null {
  const extractedName = extractGameNameFromUrl(currentPath);
  if (!extractedName) return null;

  const match = findBestMatchingGame(extractedName);
  if (!match) return null;

  return `/game/${match.slug}/`;
}

/**
 * Debug function to test URL matching (development only)
 */
export function debugUrlMatching(testUrl: string) {
  if (process.env.NODE_ENV !== 'development') return;

  console.log(`🔍 Testing URL: ${testUrl}`);

  const extractedName = extractGameNameFromUrl(testUrl);
  console.log(`📝 Extracted name: ${extractedName || 'None'}`);

  if (extractedName) {
    const match = findBestMatchingGame(extractedName);
    if (match) {
      console.log(`✅ Best match: ${match.game.name}`);
      console.log(`📊 Similarity: ${(match.similarity * 100).toFixed(1)}%`);
      console.log(`🔗 New URL: /game/${match.slug}/`);
    } else {
      console.log(`❌ No suitable match found`);
    }
  }

  const redirectUrl = getRedirectUrl(testUrl);
  console.log(`🎯 Final redirect: ${redirectUrl || 'Homepage'}`);
  console.log('---');
}
