export function getRunner<PERSON>abel(runner: string): string {
  switch (runner) {
    case 'IFRAME':
      return 'HTML5';
    case 'UNITY':
      return 'Unity';
    case 'EMULATOR_NES':
      return 'NES';
    case 'EMULATOR_GBA':
      return 'GBA';
    case 'EMULATOR_GB':
      return 'GB';
    case 'EMULATOR_GBC':
      return 'GBC';
    case 'EMULATOR_SMS':
      return 'SMS';
    case 'EMULATOR_GG':
      return 'Game Gear';
    case 'EMULATOR_SG':
      return 'SG-1000';
    case 'EMULATOR_MD':
      return 'Genesis';
    case 'EMULATOR_32X':
      return '32X';
    case 'EMULATOR_PCECD':
      return 'PC Engine';
    case 'EMULATOR_NGP':
      return 'Neo Geo';
    case 'EMULATOR_WSC':
      return 'WonderSwan';
    case 'EMULATOR_LYNX':
      return 'Lynx';
    case 'EMULATOR_JAGUAR':
      return 'Jaguar';
    case 'EMULATOR_MAME2003':
      return 'Arcade';
    case 'EMULATOR_BEETLE_PCE_FAST':
      return 'PC Engine';
    case 'EMULATOR_BEETLE_WSWAN':
      return 'WonderSwan';
    case 'EMULATOR_BEETLE_NGP':
      return 'Neo Geo';
    case 'EMULATOR_BEETLE_VB':
      return 'Virtual Boy';
    case 'EMULATOR_HANDY':
      return 'Lynx';
    case 'EMULATOR_VIRTUALJAGUAR':
      return 'Jaguar';
    case 'RUFFLE':
      return 'Flash';
    default:
      return runner;
  }
}

export function getRunnerColor(runner: string): string {
  switch (runner) {
    case 'IFRAME':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'UNITY':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'EMULATOR_NES':
    case 'EMULATOR_GBA':
    case 'EMULATOR_GB':
    case 'EMULATOR_GBC':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case 'RUFFLE':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
}