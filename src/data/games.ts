import { Game } from '@/types/Game';

export default <Game[]>[

	// PARKER GAMES
	// {
	// 	name: "prk<PERSON>'s Agario",
	// 	runner: "IFRAME",
	// 	game: "https://jptragar.glitch.me/",
	// 	thumbnail: "https://assets.playunb.com/thumbs/prkrs-agario.jpg",
	// 	width: 1200,
	// 	aspectRatio: 16 / 9
	// },

	// IFRAME GAMES
	// {
	// 	name: "1v1.lol",
	// 	runner: "IFRAME",
	// 	game: "https://ejvd3326248pklq0mtj313irgbc2vsrb-a-sites-opensocial.googleusercontent.com/gadgets/ifr?url=https://sites.google.com/site/s035r8h4/1v1.xml&container=enterprise&view=default&lang=en&country=ALL&sanitize=0&v=8d01559d545a3200&libs=core&mid=172&parent=https://sites.google.com/site/unblockedgameswtf/1v1-lol#USX6PB",
	// 	thumbnail: "https://assets.playunb.com/thumbs/1v1lol.png",
	// 	width: 1200,
	// 	aspectRatio: 16 / 9
	// }, 
	{
		name: "Baldi's Basic",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/baldis-basics/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/baldis-basic.jpg",
		width: 800,
		aspectRatio: 800 / 600
	}, {
		name: "Chrome Dino",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/chromedino/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/chrome-dino.jpg",
		width: 800,
		aspectRatio: 3 / 1
	}, {
		name: "Cookie Clicker",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/cookieclicker/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/cookie-clicker.jpg",
		width: 1080,
		aspectRatio: 3 / 2
	}, {
		name: "CSGOClicker",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/csgoclicker/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/csgoclicker.jpg",
		width: 1080,
		aspectRatio: 5 / 3
	}, {
		name: "Tanuki Sunset",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/takumiraccoon/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/tanuki-sunset.jpg",
		width: 800,
		aspectRatio: 800 / 660
	},

	// UNITY GAMES
	{
		name: "Slope",
		runner: "UNITY",
		game: "https://assets.playunb.com/games/slope_v7.json",
		thumbnail: "https://assets.playunb.com/thumbs/slope.jpg",
		aspectRatio: 1 / 1,
		width: 1200
	}, {
		name: "Falling Ball",
		runner: "UNITY",
		game: "https://assets.playunb.com/games/slope-ball.json",
		thumbnail: "https://assets.playunb.com/thumbs/falling-ball.jpg",
		aspectRatio: 16 / 9,
		width: 1200
	}, {
		name: "Rooftop Snipers",
		runner: "UNITY",
		game: "https://assets.playunb.com/games/rooftop_snipers.json",
		thumbnail: "https://assets.playunb.com/thumbs/rooftop-snipers.jpg",
		aspectRatio: 16 / 9,
		width: 793
	}, {
		name: "Subway Surfers",
		runner: "UNITY",
		game: "https://assets.playunb.com/games/surfers.json",
		thumbnail: "https://assets.playunb.com/thumbs/subway-surfers.jpg",
		aspectRatio: 16 / 9,
		width: 1200
	},

	// NES GAMES
	{
		name: "Bubble Bobble",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/bubble-bobble.nes",
		thumbnail: "https://assets.playunb.com/thumbs/bubble-bobble.jpg"
	}, {
		name: "Blades of Steel",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/blades-of-steel.nes",
		thumbnail: "https://assets.playunb.com/thumbs/blades-of-steel.jpg"
	}, {
		name: "Castlevania",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/castlevania.nes",
		thumbnail: "https://assets.playunb.com/thumbs/castlevania.jpg"
	}, {
		name: "Clu Clu Land",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/clu-clu-land.nes",
		thumbnail: "https://assets.playunb.com/thumbs/clu-clu-land.jpg"
	}, {
		name: "Contra",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/contra.nes",
		thumbnail: "https://assets.playunb.com/thumbs/contra.jpg"
	}, {
		name: "Donkey Kong",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/donkey-kong.nes",
		thumbnail: "https://assets.playunb.com/thumbs/donkey-kong.jpg"
	}, {
		name: "Double Dribble",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/double-dribble.nes",
		thumbnail: "https://assets.playunb.com/thumbs/double-dribble.jpg"
	}, {
		name: "Dr. Mario",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/dr-mario.nes",
		thumbnail: "https://assets.playunb.com/thumbs/dr-mario.jpg"
	}, {
		name: "DuckTales",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/ducktales.nes",
		thumbnail: "https://assets.playunb.com/thumbs/ducktales.jpg"
	}, {
		name: "Earthbound Zero",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/earthbound-zero.nes",
		thumbnail: "https://assets.playunb.com/thumbs/earthbound-zero.jpg"
	}, {
		name: "Excitebike",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/excitebike.nes",
		thumbnail: "https://assets.playunb.com/thumbs/excitebike.jpg"
	}, {
		name: "Final Fantasy",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/final-fantasy-1.nes",
		thumbnail: "https://assets.playunb.com/thumbs/final-fantasy-1.jpg"
	}, {
		name: "Final Fantasy 2",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/final-fantasy-ii.nes",
		thumbnail: "https://assets.playunb.com/thumbs/final-fantasy-ii.jpg"
	}, {
		name: "Final Fantasy 3",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/final-fantasy-iii.nes",
		thumbnail: "https://assets.playunb.com/thumbs/final-fantasy-iii.jpg"
	}, {
		name: "Galaga",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/galaga.nes",
		thumbnail: "https://assets.playunb.com/thumbs/galaga.jpg"
	}, {
		name: "Hoops",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/hoops.nes",
		thumbnail: "https://assets.playunb.com/thumbs/hoops.jpg"
	}, {
		name: "Ice Hockey",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/ice-hockey.nes",
		thumbnail: "https://assets.playunb.com/thumbs/ice-hockey.jpg"
	}, {
		name: "Ice Hockey - Blue Ice Edition",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/ice-hockey---blue-ice-edition.nes",
		thumbnail: "https://assets.playunb.com/thumbs/ice-hockey---blue-ice-edition.jpg"
	}, {
		name: "Kid Icarus",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/kid-icarus.nes",
		thumbnail: "https://assets.playunb.com/thumbs/kid-icarus.jpg"
	}, {
		name: "Kirby's Adventure",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/kirbys-adventure.nes",
		thumbnail: "https://assets.playunb.com/thumbs/kirbys-adventure.jpg"
	}, {
		name: "Legend of Zelda",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/legend-of-zelda.nes",
		thumbnail: "https://assets.playunb.com/thumbs/legend-of-zelda.jpg"
	}, {
		name: "Mega Man",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/mega-man.nes",
		thumbnail: "https://assets.playunb.com/thumbs/mega-man.jpg"
	}, {
		name: "Metroid",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/metroid.nes",
		thumbnail: "https://assets.playunb.com/thumbs/metroid.jpg"
	}, {
		name: "NCAA Football 2020",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/ncaa-football-2020.nes",
		thumbnail: "https://assets.playunb.com/thumbs/ncaa-football-2020.jpg"
	}, {
		name: "NFL",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/nfl.nes",
		thumbnail: "https://assets.playunb.com/thumbs/nfl.jpg"
	}, {
		name: "Q-Bert",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/q-bert.nes",
		thumbnail: "https://assets.playunb.com/thumbs/q-bert.jpg"
	}, {
		name: "RC Pro-AM",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/rc-pro-am.nes",
		thumbnail: "https://assets.playunb.com/thumbs/rc-pro-am.jpg"
	}, {
		name: "Super Mario Bros.",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/super-mario-bros.nes",
		thumbnail: "https://assets.playunb.com/thumbs/super-mario-bros.jpg"
	}, {
		name: "Super Mario Bros. 2",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/super-mario-bros-2.nes",
		thumbnail: "https://assets.playunb.com/thumbs/super-mario-bros-2.jpg"
	}, {
		name: "Super Mario Bros. 3",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/super-mario-bros-3.nes",
		thumbnail: "https://assets.playunb.com/thumbs/super-mario-bros-3.jpg"
	}, {
		name: "Tecmo Bowl",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/tecmo-bowl.nes",
		thumbnail: "https://assets.playunb.com/thumbs/tecmo-bowl.jpg"
	}, {
		name: "Tecmo Super Bowl",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/tecmo-super-bowl.nes",
		thumbnail: "https://assets.playunb.com/thumbs/tecmo-super-bowl.jpg"
	}, {
		name: "Teenage Mutant Ninja Turtles",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/teenage-mutant-ninja-turtles.nes",
		thumbnail: "https://assets.playunb.com/thumbs/teenage-mutant-ninja-turtles.jpg"
	}, {
		name: "Tetris NES",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/tetris-nes.nes",
		thumbnail: "https://assets.playunb.com/thumbs/tetris-nes.jpg"
	}, {
		name: "Totally Rad",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/totally-rad.nes",
		thumbnail: "https://assets.playunb.com/thumbs/totally-rad.jpg"
	}, {
		name: "Yoshi's Cookie",
		runner: "EMULATOR_NES",
		aspectRatio: 16 / 15,
		width: 640,
		game: "https://assets.playunb.com/games/yoshis-cookie.nes",
		thumbnail: "https://assets.playunb.com/thumbs/yoshis-cookie.jpg"
	},

	// GBA GAMES
	{
		name: "Advance Wars",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/advance-wars.gba",
		thumbnail: "https://assets.playunb.com/thumbs/advance-wars.jpg"
	}, {
		name: "Advance Wars 2",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/advance-wars-2.gba",
		thumbnail: "https://assets.playunb.com/thumbs/advance-wars-2.jpg"
	}, {
		name: "Castlevania - Aria of Sorrow",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/castlevania---aria-of-sorrow.gba",
		thumbnail: "https://assets.playunb.com/thumbs/castlevania---aria-of-sorrow.jpg"
	}, {
		name: "Fire Emblem",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/fire-emblem.gba",
		thumbnail: "https://assets.playunb.com/thumbs/fire-emblem.jpg"
	}, {
		name: "Golden Sun",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/golden-sun.gba",
		thumbnail: "https://assets.playunb.com/thumbs/golden-sun.jpg"
	}, {
		name: "Mario Kart",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/mario-kart.gba",
		thumbnail: "https://assets.playunb.com/thumbs/mario-kart.jpg"
	}, {
		name: "Mario Party Advance",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/mario-party-advance.gba",
		thumbnail: "https://assets.playunb.com/thumbs/mario-party-advance.jpg"
	}, {
		name: "Pacman World",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pacman-world.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pacman-world.jpg"
	}, {
		name: "Pokemon Ash Gray",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-ash-gray.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-ash-gray.jpg"
	}, {
		name: "Pokemon Dark Violet",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-dark-violet.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-dark-violet.jpg"
	}, {
		name: "Pokemon Emerald",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-emerald.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-emerald.jpg"
	}, {
		name: "Pokemon Flora Sky",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-flora-sky.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-flora-sky.jpg"
	}, {
		name: "Pokemon Glazed",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-glazed.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-glazed.jpg"
	}, {
		name: "Pokemon Green",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-green.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-green.jpg"
	}, {
		name: "Pokemon Light Platinum",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-light-platinum.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-light-platinum.jpg"
	}, {
		name: "Pokemon Ruby",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-ruby.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-ruby.jpg"
	}, {
		name: "Pokemon Sapphire",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-sapphire.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-sapphire.jpg"
	}, {
		name: "Pokemon Red",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-red.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-red.jpg"
	}, {
		name: "Pokemon X & Y",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/pokemon-x-and-y.gba",
		thumbnail: "https://assets.playunb.com/thumbs/pokemon-x-&-y.jpg"
	}, {
		name: "Sonic Advance",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/sonic-advance.gba",
		thumbnail: "https://assets.playunb.com/thumbs/sonic-advance.jpg"
	}, {
		name: "Wario Ware",
		runner: "EMULATOR_GBA",
		aspectRatio: 3 / 2,
		width: 480,
		game: "https://assets.playunb.com/games/wario-ware.gba",
		thumbnail: "https://assets.playunb.com/thumbs/wario-ware.jpg"
	},

	// Flash Games?!?!?!
	{
		name: "4th and Goal 2018",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/4th-and-goal-2018.swf",
		thumbnail: "https://assets.playunb.com/thumbs/4th-and-goal-2018.jpg",
		width: 800,
		aspectRatio: 800/588
	}, {
		name: "B-Cubed",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/b-cubed.swf",
		thumbnail: "https://assets.playunb.com/thumbs/b-cubed.jpg",
		width: 800,
		aspectRatio: 80/72
	}, {
		name: "Bloons Tower Defense 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/bloons-tower-defense-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/bloons-tower-defense-2.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Bloons Tower Defense 1",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/bloons-tower-defense-1.swf",
		thumbnail: "https://assets.playunb.com/thumbs/bloons-tower-defense-1.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "2048",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/2048.swf",
		thumbnail: "https://assets.playunb.com/thumbs/2048.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Bloxorz",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/bloxorz.swf",
		thumbnail: "https://assets.playunb.com/thumbs/bloxorz.jpg",
		width: 800,
		aspectRatio: 11/6
	}, {
		name: "BMX Park",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/bmx-park.swf",
		thumbnail: "https://assets.playunb.com/thumbs/bmx-park.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Bubble Trouble",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/bubble-trouble.swf",
		thumbnail: "https://assets.playunb.com/thumbs/bubble-trouble.jpg",
		width: 800,
		aspectRatio: 800/518
	}, {
		name: "Cubefield",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/cubefield.swf",
		thumbnail: "https://assets.playunb.com/thumbs/cubefield.jpg",
		width: 800,
		aspectRatio: 800/588
	}, {
		name: "Dad N Me",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/dad-n-me.swf",
		thumbnail: "https://assets.playunb.com/thumbs/dad-n-me.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Dragon Fist 3",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/dragon-fist-3.swf",
		thumbnail: "https://assets.playunb.com/thumbs/dragon-fist-3.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Dragon Ball Z Devolution",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/dragon-ball-z-devolution.swf",
		thumbnail: "https://assets.playunb.com/thumbs/dragon-ball-z-devolution.jpg",
		width: 800,
		aspectRatio: 80/47
	}, {
		name: "Duck Life 4",
		runner: "RUFFLE",
		game: "https://cdn.jsdelivr.net/gh/ellieeet123/swf/swf/duck-life-4.swf",
		thumbnail: "https://assets.playunb.com/thumbs/duck-life-4.jpg",
		aspectRatio: 75 / 48,
		width: 750
	}, {
		name: "Duck Life 3",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/duck-life-3.swf",
		thumbnail: "https://assets.playunb.com/thumbs/duck-life-3.jpg",
		width: 800,
		aspectRatio: 800/502
	 },	{
		name: "Duck Life 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/duck-life-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/duck-life-2.jpg",
		width: 800,
		aspectRatio: 800/588
	 },	{
		name: "Duck Life 1",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/duck-life-1.swf",
		thumbnail: "https://assets.playunb.com/thumbs/duck-life-1.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Electric Man 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/electric-man-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/electric-man-2.jpg",
		width: 800,
		aspectRatio: 80/49
	}, {
		name: "Escape the Bathroom",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/escape-the-bathroom.swf",
		thumbnail: "https://assets.playunb.com/thumbs/escape-the-bathroom.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Escape the Car",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/escape-the-car.swf",
		thumbnail: "https://assets.playunb.com/thumbs/escape-the-car.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Escape the Closet",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/escape-the-closet.swf",
		thumbnail: "https://assets.playunb.com/thumbs/escape-the-closet.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Escape the Freezer",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/escape-the-freezer.swf",
		thumbnail: "https://assets.playunb.com/thumbs/escape-the-freezer.jpg",
		width: 800,
		aspectRatio: 80/48
	}, {
		name: "Feudalism 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/feudalism-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/feudalism-2.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Gunblood",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/gunblood.swf",
		thumbnail: "https://assets.playunb.com/thumbs/gunblood.jpg",
		width: 800,
		aspectRatio: 800/462
	}, {
		name: "Gun Mayhem",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/gun-mayhem.swf",
		thumbnail: "https://assets.playunb.com/thumbs/gun-mayhem.jpg",
		width: 800,
		aspectRatio: 80/52
	}, { name: "Gun Mayhem 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/gun-mayhem-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/gun-mayhem-2.jpg",
		width: 800,
		aspectRatio: 80/53
	}, {
		name: "Helicopter",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/helicopter.swf",
		thumbnail: "https://assets.playunb.com/thumbs/helicopter.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Henry Stickmin - Breaking the Bank",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/henry-stickmin---breaking-the-bank.swf",
		thumbnail: "https://assets.playunb.com/thumbs/henry-stickmin---breaking-the-bank.jpg",
		width: 800,
		aspectRatio: 80/58
	}, {
		name: "Henry Stickmin - Crossing the Pit",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/henry-stickmin---crossing-the-pit.swf",
		thumbnail: "https://assets.playunb.com/thumbs/henry-stickmin---crossing-the-pit.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Henry Stickmin - Stealing the Diamond",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/henry-stickmin---stealing-the-diamond.swf",
		thumbnail: "https://assets.playunb.com/thumbs/henry-stickmin---stealing-the-diamond.jpg",
		width: 800,
		aspectRatio: 4/3
	}, { name: "Impossible Quiz",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/impossible-quiz.swf",
		thumbnail: "https://assets.playunb.com/thumbs/impossible-quiz.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Impossible Quiz 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/impossible-quiz-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/impossible-quiz-2.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Kill the Spartan",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/kill-the-spartan.swf",
		thumbnail: "https://assets.playunb.com/thumbs/kill-the-spartan.jpg",
		width: 800,
		aspectRatio: 800/532
	}, {
		name: "Learn to Fly",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/learn-to-fly.swf",
		thumbnail: "https://assets.playunb.com/thumbs/learn-to-fly.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Learn to Fly 2",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/learn-to-fly-2.swf",
		thumbnail: "https://assets.playunb.com/thumbs/learn-to-fly-2.jpg",
		width: 800,
		aspectRatio: 80/59
	}, {
		name: "Line rider 1",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/line-rider-1.swf",
		thumbnail: "https://assets.playunb.com/thumbs/line-rider-1.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Madness Project Nexus",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/madness-project-nexus.swf",
		thumbnail: "https://assets.playunb.com/thumbs/madness-project-nexus.jpg",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Minesweeper",
		runner: "RUFFLE",
		game: "https://assets.playunb.com/games/minesweeper.swf",
		thumbnail: "https://assets.playunb.com/thumbs/minesweeper.jpg",
		width: 800,
		aspectRatio: 4/3
	},
	// ... existing code ...
	// IFRAME GAMES
	{
		name: "2048 (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/2048/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/2048.png",
		width: 800,
		aspectRatio: 1
	}, {
		name: "A Dark Room",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/a-dark-room/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/a-dark-room.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Agario Minigame",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/agario-minigame/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/agario-minigame.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Align 4",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/align-4/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/align-4.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Astray",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/astray/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/astray.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Basketball Stars",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/basketball-stars/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/basketball-stars.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Basket Random",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/basket-random/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/basket-random.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Bike Mania",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/bike-mania/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/bike-mania.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Boxing Random",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/boxing-random/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/boxing-random.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Breaklock",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/breaklock/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/break-lock.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Celeste",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/celeste/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/celeste.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Chroma",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/chroma/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/the-chroma-incident.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Colorun",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/colorun/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/colorun.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Cookie (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/cookie/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/cookie-clicker.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Cubefield (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/cubefield/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/cube-field.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Dinosaur (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/dinosaur/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/dinosaur.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Donut Boy",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/donut-boy/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/donut-boy.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Doodle Jump",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/doodle-jump/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/doodle-jump.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Fireboy and Watergirl Forest Temple",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/fireboy-and-watergirl-forest-temple/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/fireboy-and-watergirl-forest-temple.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Fire Truck Dash",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/fire-truck-dash/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/fire-truck-dash.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Flakboy",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/flakboy/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/flakboy.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Flappy 2048",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/flappy-2048/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/flappy-2048.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Flappybird (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/flappybird/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/flappy-bird.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Friday Night Funkin' VS EX",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/friday-night-funkin--vs-ex/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/friday-night-funkin-vs-ex.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Friday Night Funkin' Week 6",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/friday-night-funkin--week-6/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/friday-night-funkin-week-6.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Friendly Fire",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/friendlyfire/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/friendly-fire.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Geometry Dash Remastered",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/geometry-dash-remastered/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/geometry-dash-remastered.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Google Snake",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/google-snake/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/google-snake.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Google Solitaire",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/google-solitaire/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/google-solitaire.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Gopher Kart",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/gopher-kart/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/gopher-kart.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Gun Knight",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/gun-knight/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/gun-knight.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Hacker Typer",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/hacker-typer/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/hacker-typer.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "HexGL",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/hexgl/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/hexgl.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Hextris",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/hextris/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/hextris.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Hill Racing",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/hill-racing/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/hill-racing.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Minecraft Classic",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/mc-classic/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/mc-classic.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Microsoft Flight Simulator",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/microsoft-flight-simulator/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/microsoft-flight-simulator.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Minesweeper (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/minesweeper/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/minesweeper.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Moto X3M",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/moto-x3m/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/moto-x3m.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Moto X3M Pool Party",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/moto-x3m-pool-party/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/moto-x3m-pool-party.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Moto X3M Spooky Land",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/moto-x3m-spooky-land/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/moto-x3m-spooky-land.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Moto X3M Winter",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/moto-x3m-winter/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/moto-x3m-winter.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Muffin Knight",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/muffin-knight/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/muffin-knight.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Pacman (HTML)",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/pacman/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/pacman-html.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Radius Raid",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/radius-raid/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/radius-raid.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Retro Bowl",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/retro-bowl/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/retro-bowl.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Ritz",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/ritz/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/ritz.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Run 3",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/run-3/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/run-3.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Running Bot Xmas Gifts",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/running-bot-xmas-gifts/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/running-bot-xmas-gifts.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Sans Fight",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/sans-fight/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/sans-fight.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Super Mario 64",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/sm64/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/sm64.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Suika Game",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/suika-game/index.html",
		thumbnail: "https://assets.playunb.com/html/suika-game/screenshot.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Soccer Random",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/soccer-random/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/soccer-random.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Space Invaders",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/spaceinvaders/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/space-invaders.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Super Smash Flash 2",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/super-smash-flash-2a/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/super-smash-flash.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Tank Trouble",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/tanktrouble/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/tanktrouble.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "There Is No Game",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/there-is-no-game/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/there-is-no-game.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Unfair Dyne",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/unfair-dyne/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/unfair-dyne.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Uno",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/uno/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/uno.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Vex 3",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/vex3/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/vex-3.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Vex 4",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/vex4/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/vex-4.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Vex 5",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/vex5/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/vex-5.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Volley Random",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/volley-random/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/volley-random.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Rolling Sky",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/webgl-rollingsky/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/rolling-sky.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Wordle",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/wordle/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/wordle.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Zombotron",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/zombotron/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/zombotron.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Zombotron 2",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/zombotron-2/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/zombotron-2.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Zombotron 2 Time Machine",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/zombotron-2-time-machine/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/zombotron-2-time-machine.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Zombs Royale",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/html/zombsroyale/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/zombsroyale.png",
		width: 800,
		aspectRatio: 4/3
	}, {
		name: "Bad Time Simulator",
		runner: "IFRAME",
		game: "https://assets.playunb.com/games/undertale-sans-fight/index.html",
		thumbnail: "https://assets.playunb.com/thumbs/bad-time-simulator.png",
		width: 800,
		aspectRatio: 4/3
	}
];
